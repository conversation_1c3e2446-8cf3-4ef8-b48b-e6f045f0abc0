"""
Simple test suite to validate the core functionality
"""

import unittest
import tempfile
import os
import shutil
from unittest.mock import patch, MagicMock
from scripts.structurer import structure_data


class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality without complex dependencies"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
        
    def test_structure_data_basic(self):
        """Test basic data structuring"""
        raw = [{"raw_question": "What is 2+2?", "raw_answer": "4"}]
        structured = structure_data(raw)
        
        self.assertEqual(len(structured), 1)
        self.assertIn('question', structured[0])
        self.assertIn('answer', structured[0])
        self.assertIn('type', structured[0])
        self.assertIn('equations', structured[0])
        self.assertIn('tables', structured[0])
        self.assertIn('figures', structured[0])
        
    def test_structure_data_multiple_choice(self):
        """Test multiple choice detection"""
        raw = [{"raw_question": "What is (A) correct (B) wrong?", "raw_answer": "A"}]
        structured = structure_data(raw)
        
        self.assertEqual(structured[0]['type'], 'multiple-choice')
        
    def test_structure_data_open_ended(self):
        """Test open-ended question detection"""
        raw = [{"raw_question": "Explain the concept", "raw_answer": "Detailed explanation"}]
        structured = structure_data(raw)
        
        self.assertEqual(structured[0]['type'], 'open-ended')
        
    def test_structure_data_equations(self):
        """Test equation extraction"""
        raw = [{"raw_question": "Solve $x^2 + 1 = 0$", "raw_answer": "Complex solutions"}]
        structured = structure_data(raw)
        
        self.assertGreater(len(structured[0]['equations']), 0)
        
    def test_structure_data_empty(self):
        """Test with empty data"""
        structured = structure_data([])
        self.assertEqual(len(structured), 0)
        
    def test_structure_data_unicode(self):
        """Test with Unicode content"""
        raw = [{"raw_question": "Quelle est la réponse? α + β = ?", "raw_answer": "Réponse française"}]
        structured = structure_data(raw)
        
        self.assertEqual(len(structured), 1)
        self.assertIn('question', structured[0])
        
    @patch('scripts.parser.pdfplumber.open')
    def test_pdf_parser_mock(self, mock_pdfplumber):
        """Test PDF parser with mocked pdfplumber"""
        from scripts.parser import parse_pdf
        
        # Mock the PDF structure
        mock_page = MagicMock()
        mock_page.extract_text.return_value = "Question 1: What is Python?\nAnswer: A programming language"
        
        mock_pdf = MagicMock()
        mock_pdf.pages = [mock_page]
        mock_pdfplumber.return_value.__enter__.return_value = mock_pdf
        
        result = parse_pdf("dummy.pdf")
        self.assertIsInstance(result, list)
        
    @patch('scripts.parser.textract.process')
    def test_tex_parser_mock(self, mock_textract):
        """Test TeX parser with mocked textract"""
        from scripts.parser import parse_tex
        
        mock_textract.return_value = b"\\question{What is LaTeX?}\\answer{A typesetting system}"
        
        result = parse_tex("dummy.tex")
        self.assertIsInstance(result, list)
        
    @patch('scripts.parser.webdriver.Chrome')
    def test_html_parser_mock(self, mock_driver):
        """Test HTML parser with mocked Selenium"""
        from scripts.parser import parse_html
        
        mock_driver_instance = MagicMock()
        mock_driver.return_value = mock_driver_instance
        mock_driver_instance.page_source = """
        <div class="quiz-question">
            <p class="question-text">What is HTML?</p>
            <p class="answer-text">HyperText Markup Language</p>
        </div>
        """
        
        result = parse_html("dummy.html")
        self.assertIsInstance(result, list)
        
    @patch('scripts.fetcher.requests.get')
    def test_fetcher_mock(self, mock_get):
        """Test document fetcher with mocked requests"""
        from scripts.fetcher import fetch_document
        
        mock_response = MagicMock()
        mock_response.content = b"PDF content"
        mock_get.return_value = mock_response
        
        with patch('os.makedirs'):
            result = fetch_document("https://example.com/test.pdf", "pdf")
            self.assertTrue(result.endswith('.pdf'))
            
    def test_edge_case_combinations(self):
        """Test combinations of edge cases"""
        edge_cases = [
            {"raw_question": "", "raw_answer": ""},  # Empty
            {"raw_question": "Q with $\\alpha + \\beta$", "raw_answer": "Math"},  # Math
            {"raw_question": "Français: é, à, ç", "raw_answer": "Accents"},  # Unicode
            {"raw_question": "Table question", "raw_answer": "Table answer"},  # Table
            {"raw_question": "Figure question", "raw_answer": "Figure answer"},  # Figure
            {"raw_question": "Multiple (A) choice (B) question", "raw_answer": "A"},  # MC
        ]
        
        structured = structure_data(edge_cases)
        
        # Should handle all edge cases without crashing
        self.assertEqual(len(structured), len(edge_cases))
        
        # Check that different types are detected
        types = [item['type'] for item in structured]
        self.assertIn('multiple-choice', types)
        self.assertIn('open-ended', types)
        
    def test_filename_fix_validation(self):
        """Test that the filename fix in main.py is correct"""
        # Check that the correct files exist in the data directory
        correct_pdf = 'data/chem_exam.pdf'
        correct_tex = 'data/math_exam.tex'

        # These files should exist (when run from project root)
        if os.path.exists('data/'):
            # At least one of the expected files should exist
            files_exist = os.path.exists(correct_pdf) or os.path.exists(correct_tex)
            self.assertTrue(files_exist, "Expected data files should exist")


class TestErrorHandling(unittest.TestCase):
    """Test error handling scenarios"""
    
    def test_structure_data_missing_fields(self):
        """Test structuring with missing required fields"""
        raw = [{"raw_question": "Question only"}]  # Missing raw_answer
        
        with self.assertRaises(KeyError):
            structure_data(raw)
            
    def test_structure_data_invalid_input(self):
        """Test structuring with invalid input types"""
        with self.assertRaises(TypeError):
            structure_data("not a list")
            
    def test_parse_document_unsupported_format(self):
        """Test parse_document with unsupported format"""
        from scripts.parser import parse_document

        with self.assertRaises(ValueError):
            parse_document("test.doc", "doc")


if __name__ == '__main__':
    unittest.main(verbosity=2)
