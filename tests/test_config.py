"""
Test Configuration and Utilities

This module provides configuration and utility functions for the comprehensive test suite.
"""

import os
import tempfile
import shutil
from typing import Dict, List, Any


class TestConfig:
    """Configuration class for test settings"""
    
    # Performance test thresholds
    MAX_PARSING_TIME = 30  # seconds
    MAX_MEMORY_INCREASE = 500  # MB
    MAX_CONCURRENT_TIME = 60  # seconds
    
    # Test data sizes
    LARGE_DATASET_SIZE = 1000
    CONCURRENT_WORKERS = 10
    
    # File paths
    TEST_DATA_DIR = "test_data"
    OUTPUT_DIR = "test_outputs"
    
    @classmethod
    def get_temp_dir(cls) -> str:
        """Create and return a temporary directory for tests"""
        return tempfile.mkdtemp()
    
    @classmethod
    def cleanup_temp_dir(cls, temp_dir: str) -> None:
        """Clean up temporary directory"""
        shutil.rmtree(temp_dir, ignore_errors=True)


class TestDataGenerator:
    """Utility class for generating test data"""
    
    @staticmethod
    def generate_pdf_content(num_questions: int = 10) -> str:
        """Generate PDF-like content with questions and answers"""
        content = ""
        for i in range(num_questions):
            content += f"Question {i+1}: What is the answer to question {i+1}?\n"
            content += f"Answer: This is answer {i+1}\n\n"
        return content
    
    @staticmethod
    def generate_tex_content(num_questions: int = 10) -> str:
        """Generate TeX content with questions and answers"""
        content = "\\documentclass{article}\n\\begin{document}\n\n"
        for i in range(num_questions):
            content += f"\\question{{Question {i+1}: What is $x_{i+1}$?}}\n"
            content += f"\\answer{{Answer {i+1}: $x_{i+1} = {i+1}$}}\n\n"
        content += "\\end{document}\n"
        return content
    
    @staticmethod
    def generate_html_content(num_questions: int = 10) -> str:
        """Generate HTML content with quiz questions"""
        content = "<html><body>\n"
        for i in range(num_questions):
            content += f"""
            <div class="quiz-question">
                <p class="question-text">Question {i+1}: What is the answer?</p>
                <p class="answer-text">Answer {i+1}</p>
            </div>
            """
        content += "</body></html>\n"
        return content
    
    @staticmethod
    def generate_french_math_content() -> str:
        """Generate French mathematical content similar to real data"""
        return """
        \\question{Calculer la probabilité P(A ∩ B)}
        \\answer{P(A ∩ B) = P(A) × P(B|A) = 0,25 × 0,90 = 0,225}
        
        \\question{Démontrer que pour tout entier naturel n, $u_n = \\frac{a_n}{a_n - 1}$}
        \\answer{Par définition de $a_n = \\frac{u_n}{u_n - 1}$, on obtient $u_n = \\frac{a_n}{a_n - 1}$}
        
        \\begin{tikzpicture}
        \\node (A) at (0,0) {A};
        \\node (B) at (2,0) {B};
        \\node (C) at (4,0) {C};
        \\draw (A) -- (B) -- (C);
        \\end{tikzpicture}
        """
    
    @staticmethod
    def generate_teas_quiz_content() -> str:
        """Generate TEAS-style quiz content"""
        return """
        <html>
        <head><title>TEAS Practice Test</title></head>
        <body>
        <div class="quiz-question">
            <p class="question-text">Which sentence below is written in correct standard English?</p>
            <p class="answer-text">I am going to the store.</p>
        </div>
        <div class="quiz-question">
            <p class="question-text">Which organelle is responsible for the production of ATP in a cell?</p>
            <p class="answer-text">Mitochondria</p>
        </div>
        <div class="quiz-question">
            <p class="question-text">What is the value of x in the equation 3x - 7 = 14?</p>
            <p class="answer-text">x = 7</p>
        </div>
        </body>
        </html>
        """
    
    @staticmethod
    def generate_edge_case_data() -> List[Dict[str, Any]]:
        """Generate edge case test data"""
        return [
            {"raw_question": "", "raw_answer": ""},  # Empty
            {"raw_question": "Q with $\\alpha + \\beta = \\gamma$", "raw_answer": "Mathematical"},
            {"raw_question": "Français: é, à, ç, ñ, ü", "raw_answer": "Unicode characters"},
            {"raw_question": "Table data analysis", "raw_answer": "Table interpretation"},
            {"raw_question": "Figure interpretation", "raw_answer": "Figure description"},
            {"raw_question": "Multiple (A) choice (B) question (C) format", "raw_answer": "A"},
            {"raw_question": "Very " + "long " * 100 + "question", "raw_answer": "Short answer"},
            {"raw_question": "Special chars: @#$%^&*()", "raw_answer": "Symbol handling"},
            {"raw_question": "Line\nbreak\nquestion", "raw_answer": "Multi\nline\nanswer"},
            {"raw_question": "JSON: {\"key\": \"value\"}", "raw_answer": "JSON content"},
        ]


class TestReporter:
    """Utility class for test reporting"""
    
    @staticmethod
    def generate_test_report(results: Dict[str, Any]) -> str:
        """Generate a comprehensive test report"""
        report = "# Comprehensive Test Suite Report\n\n"
        
        report += f"## Summary\n"
        report += f"- Total Tests: {results.get('total_tests', 0)}\n"
        report += f"- Passed: {results.get('passed', 0)}\n"
        report += f"- Failed: {results.get('failed', 0)}\n"
        report += f"- Errors: {results.get('errors', 0)}\n"
        report += f"- Success Rate: {results.get('success_rate', 0):.2f}%\n\n"
        
        if results.get('failed_tests'):
            report += "## Failed Tests\n"
            for test in results['failed_tests']:
                report += f"- {test}\n"
            report += "\n"
        
        if results.get('performance_metrics'):
            report += "## Performance Metrics\n"
            metrics = results['performance_metrics']
            report += f"- Average parsing time: {metrics.get('avg_parse_time', 0):.2f}s\n"
            report += f"- Memory usage: {metrics.get('memory_usage', 0):.2f}MB\n"
            report += f"- Concurrent processing time: {metrics.get('concurrent_time', 0):.2f}s\n\n"
        
        report += "## Edge Cases Covered\n"
        edge_cases = [
            "Empty files and content",
            "Special characters and Unicode",
            "Large datasets and files",
            "Network failures and timeouts",
            "Malformed input data",
            "OCR fallback scenarios",
            "TikZ figure detection",
            "French mathematical notation",
            "Dynamic HTML content",
            "Concurrent processing",
            "Memory stress testing",
            "File permission errors",
            "Missing dependencies",
            "Invalid file formats"
        ]
        
        for case in edge_cases:
            report += f"- ✓ {case}\n"
        
        return report


# Test data samples for quick access
SAMPLE_PDF_CONTENT = TestDataGenerator.generate_pdf_content(5)
SAMPLE_TEX_CONTENT = TestDataGenerator.generate_tex_content(5)
SAMPLE_HTML_CONTENT = TestDataGenerator.generate_html_content(5)
SAMPLE_FRENCH_MATH = TestDataGenerator.generate_french_math_content()
SAMPLE_TEAS_QUIZ = TestDataGenerator.generate_teas_quiz_content()
SAMPLE_EDGE_CASES = TestDataGenerator.generate_edge_case_data()
