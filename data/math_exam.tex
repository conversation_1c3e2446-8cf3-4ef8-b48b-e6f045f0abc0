\documentclass[11pt,a4paper,french]{article}
\usepackage[T1]{fontenc}
\usepackage[utf8]{inputenc}
\usepackage{fourier}
\usepackage[scaled=0.875]{helvet}
\renewcommand{\ttdefault}{lmtt}
\usepackage{makeidx}
\usepackage{amsmath,amssymb}
\usepackage{fancybox}
\usepackage[normalem]{ulem}
\usepackage{pifont}
\usepackage{lscape}
\usepackage{multicol}
\usepackage{mathrsfs}
\usepackage{tabularx}
\usepackage{multirow}
\usepackage{enumitem}
\usepackage{textcomp}
\newcommand{\euro}{\eurologo{}}
%Merci à <PERSON> pour le sujet
%Tapuscrit : François Kriegk
%Relecture : Denis Vergès
\usepackage{pst-plot,pst-tree,pstricks,pst-node,pst-text}
\usepackage{pst-eucl,pst-3dplot,pstricks-add}
\usepackage{tikz,pgfplots}
\usepackage{esvect}
\newcommand{\R}{\mathbb{R}}
\newcommand{\N}{\mathbb{N}}
\newcommand{\D}{\mathbb{D}}
\newcommand{\Z}{\mathbb{Z}}
\newcommand{\Q}{\mathbb{Q}}
\newcommand{\C}{\mathbb{C}}
\usepackage[left=3.5cm, right=3.5cm, top=2cm, bottom=3cm,marginparwidth=0pt]{geometry}
\headheight5 mm
\newcommand{\vect}[1]{\overrightarrow{\,\mathstrut#1\,}}
\renewcommand{\theenumi}{\textbf{\arabic{enumi}}}
\renewcommand{\labelenumi}{\textbf{\theenumi.}}
\renewcommand{\theenumii}{\textbf{\alph{enumii}}}
\renewcommand{\labelenumii}{\textbf{\theenumii.}}
\def\Oij{$\left(\text{O}~~;~~\vect{\imath},~\vect{\jmath}\right)$}
\def\Oijk{$\left(\text{O}~~;~~\vect{\imath},~\vect{\jmath},~\vect{k}\right)$}
\def\Ouv{$\left(\text{O}~~;~~\vect{u},~\vect{v}\right)$}
\newcommand{\e}{\text{e}}
\usepackage{fancyhdr}
\usepackage[dvips]{hyperref}
\hypersetup{%
pdfauthor = {APMEP},
pdfsubject = {Baccalauréat Spécialité},
pdftitle = {Amérique du Nord Sujet 1 21 mai 2025},
allbordercolors = white,
pdfstartview=FitH}
\usepackage{babel}
\usepackage[np]{numprint}
\marginpar{\rotatebox{90}{\textbf{A. P{}. M. E. P{}.}}}
\renewcommand\arraystretch{1.}
\frenchsetup{StandardLists=true}
\begin{document}
\setlength\parindent{0mm}
\rhead{\textbf{A. P{}. M. E. P{}.}}
\lhead{\small Baccalauréat spécialité sujet 1}
\lfoot{\small{Amérique du Nord}}
\rfoot{\small{21 mai 2025}}
\pagestyle{fancy}
\thispagestyle{empty}

\begin{center}{\Large\textbf{\decofourleft~Baccalauréat Amérique du Nord 21 mai 2025~\decofourright\\[7pt]Sujet 1\\[7pt] ÉPREUVE D'ENSEIGNEMENT DE SPÉCIALITÉ}}
\end{center}


%	La qualité de la rédaction, la clarté et la précision des raisonnements seront prises en compte dans l'appréciation de la copie. Les traces de recherche, même incomplètes ou infructueuses, seront valorisées.

\medskip

\textbf{\textsc{Exercice 1} \hfill 6 points}

\medskip
Pour accéder au réseau privé d'une entreprise depuis l'extérieur, les connexions des employés transitent aléatoirement via trois serveurs distants différents, notés A, B et C. Ces serveurs ont des caractéristiques techniques différentes et les connexions se répartissent de la manière suivante :

\begin{itemize}
\item 25~\% des connexions transitent via le serveur A;
\item 15~\% des connexions transitent via le serveur B;
\item le reste des connexions s'effectue via le serveur C.
\end{itemize}

\medskip

Les connexions à distance sont parfois instables et, lors du fonctionnement normal des serveurs, les utilisateurs peuvent subir des déconnexions pour différentes raisons (saturation des serveurs, débit internet insuffisant, attaques malveillantes, mises à jour de logiciels, etc.).

\medskip

On dira qu'une connexion est stable si l'utilisateur ne subit pas de déconnexion après son identification aux serveurs. L'équipe de maintenance informatique a observé statistiquement que, dans le cadre d'un fonctionnement habituel des serveurs :

\begin{itemize}
\item 90~\% des connexions via le serveur A sont stables;
\item 80~\% des connexions via le serveur B sont stables;
\item 85~\% des connexions via le serveur C sont stables.
\end{itemize}

Les parties \textbf{A} et \textbf{B} sont indépendantes l'une de l'autre et peuvent être traitées séparément.

\medskip

\textbf{Partie A}

\medskip

On s'intéresse au hasard à l'état d'une connexion effectuée par un employé de l'entreprise. On considère les évènements suivants :

\begin{itemize}
\item $A$ : \og La connexion s'est effectuée via le serveur A\fg{};
\item $B$ : \og La connexion s'est effectuée via le serveur B\fg{};
\item $C$ : \og La connexion s'est effectuée via le serveur C\fg{};
\item $S$ : \og La connexion est stable\fg.
\end{itemize}

On note $\overline{S}$ l'évènement contraire de l'évènement $S$.

\begin{enumerate}
\item Recopier et compléter l'arbre pondéré ci-dessous modélisant la situation de l'énoncé.

	%:-+-+-+- Engendré par : http://math.et.info.free.fr/TikZ/Arbre/
\begin{center}
	% Racine à Gauche, développement vers la droite
\begin{tikzpicture}[xscale=1,yscale=1]
	% Styles (MODIFIABLES)
	\tikzstyle{fleche}=[->,>=latex,thick]
	\tikzstyle{noeud}=[fill=white,circle,inner sep=2pt]
	\tikzstyle{feuille}=[fill=white,circle,inner sep=2pt]
	\tikzstyle{etiquette}=[midway,fill=white, inner xsep=3pt, inner ysep=1.5pt]
	% Dimensions (MODIFIABLES)
	\def\DistanceInterNiveaux{3}
	\def\DistanceInterFeuilles{0.65}
	% Dimensions calculées (NON MODIFIABLES)
	\def\NiveauA{(0)*\DistanceInterNiveaux}
	\def\NiveauB{(1)*\DistanceInterNiveaux}
	\def\NiveauC{(2)*\DistanceInterNiveaux}
	\def\InterFeuilles{(-1)*\DistanceInterFeuilles}
	% Noeuds (MODIFIABLES : Styles et Coefficients d'InterFeuilles)
	\node[noeud] (R) at ({\NiveauA},{(2.5)*\InterFeuilles}) {$$};
	\node[noeud] (Ra) at ({\NiveauB},{(0.5)*\InterFeuilles}) {$A$};
	\node[feuille] (Raa) at ({\NiveauC},{(0)*\InterFeuilles}) {$S$};
	\node[feuille] (Rab) at ({\NiveauC},{(1)*\InterFeuilles}) {$\overline{S}$};
	\node[noeud] (Rb) at ({\NiveauB},{(2.5)*\InterFeuilles}) {$B$};
	\node[feuille] (Rba) at ({\NiveauC},{(2)*\InterFeuilles}) {$S$};
	\node[feuille] (Rbb) at ({\NiveauC},{(3)*\InterFeuilles}) {$\overline{S}$};
	\node[noeud] (Rc) at ({\NiveauB},{(4.5)*\InterFeuilles}) {$C$};
	\node[feuille] (Rca) at ({\NiveauC},{(4)*\InterFeuilles}) {$S$};
	\node[feuille] (Rcb) at ({\NiveauC},{(5)*\InterFeuilles}) {$\overline{S}$};
	% Arcs (MODIFIABLES : Styles)
	\draw[fleche] (R.east)--(Ra.west) node[etiquette] {$\dots$};
	\draw[fleche] (Ra.east)--(Raa.west) node[etiquette] {$\dots$};
	\draw[fleche] (Ra.east)--(Rab.west) node[etiquette] {$\dots$};
		\draw[fleche] (R.east)--(Rb.west) node[etiquette] {$\dots$};
	\draw[fleche] (Rb.east)--(Rba.west) node[etiquette] {$\dots$};
	\draw[fleche] (Rb.east)--(Rbb.west) node[etiquette] {$\dots$};
	\draw[fleche] (R.east)--(Rc.west) node[etiquette] {$\dots$};
	\draw[fleche] (Rc.east)--(Rca.west) node[etiquette] {$\dots$};
	\draw[fleche] (Rc.east)--(Rcb.west) node[etiquette] {$\dots$};
\end{tikzpicture}
\end{center}
	%:-+-+-+-+- Fin

%		%Version pstree : imparfaite
%		\psset{treemode=R, levelsep=3cm, treesep=0.65cm, arrows=->}
%
%		\newpsobject{fleche}{arrows}{->,arrowsize=0.15,linewidth=0.8pt}
%		\newpsobject{etiquette}{nodeputpos}{pos=0.5,fillstyle=solid,fillcolor=white,framesep=3pt}
%
%		\pstree{\Tcircle[fillstyle=none]{}}{
%			\pstree{\Tcircle[fillstyle=none]{$A$}~[etiquette]{$\dots$}}
%				{\Tcircle[fillstyle=none]{$S$}~[etiquette]{$\dots$}
%				\Tcircle[fillstyle=none]{$\overline{S}$}~[etiquette]{$\dots$}}
%			\pstree{\Tcircle[fillstyle=none]{$B$}~[etiquette]{$\dots$}}
%				{\Tcircle[fillstyle=none]{$S$}~[etiquette]{$\dots$}
%				\Tcircle[fillstyle=none]{$\overline{S}$}~[etiquette]{$\dots$}}
%			\pstree{\Tcircle[fillstyle=none]{$C$}~[etiquette]{$\dots$}}
%				{\Tcircle[fillstyle=none]{$S$}~[etiquette]{$\dots$}
%				\Tcircle[fillstyle=none]{$\overline{S}$}~[etiquette]{$\dots$}}
%		}
		\newpage

\item Démontrer que la probabilité que la connexion soit stable et passe par le serveur B est égale à $0,12$.
\item Calculer la probabilité $P\left(C \cap \overline{S}\right)$ et interpréter le résultat dans le contexte de l'exercice.
\item Démontrer que la probabilité de l'évènement $S$ est $P(S) = 0,855$.
\item On suppose désormais que la connexion est stable.

Calculer la probabilité que la connexion ait eu lieu depuis le serveur B.

\emph{On donnera la valeur arrondie au millième}.
\end{enumerate}

\medskip

\textbf{Partie B}

\medskip
D'après la \textbf{partie A}, la probabilité qu'une connexion soit \textbf{instable} est égale à $0,145$.

\begin{enumerate}
\item Dans le but de détecter les dysfonctionnements de serveurs, on étudie un échantillon de 50 connexions au réseau, ces connexions étant choisies au hasard. On suppose que le nombre de connexions est suffisamment important pour que ce choix puisse être assimilé à un tirage avec remise.

\smallskip

On désigne par $X$ la variable aléatoire égale au nombre de connexions instables au réseau de l'entreprise, dans cet échantillon de 50 connexions.

	\begin{enumerate}
		\item On admet que la variable aléatoire $X$ suit une loi binomiale. Préciser ses paramètres.
		\item Donner la probabilité qu'au plus huit connexions soient instables. \emph{On donnera la valeur arrondie au millième}.
	\end{enumerate}

\item Dans cette question, on constitue désormais un échantillon de $n$ connexions, toujours dans les mêmes conditions, où $n$ désigne un entier naturel strictement positif. On note $X_{n}$ la variable aléatoire égale aux nombres de connexions instables et on admet que $X_{n}$ suit une loi binomiale de paramètres $n$ et 0,145.
	\begin{enumerate}
		\item Donner l'expression en fonction de $n$ de la probabilité $p_{n}$ qu'au moins une connexion de cet échantillon soit instable.
		\item Déterminer, en justifiant, la plus petite valeur de l'entier naturel $n$ telle que la probabilité $p_{n}$ est supérieure ou égale à 0,99.
	\end{enumerate}
\item On s'intéresse à la variable aléatoire $F_{n}$ égale à la fréquence de connexions instables dans un échantillon de $n$ connexions, où $n$ désigne un entier naturel strictement positif.

On a donc $F_{n}=\dfrac{X_{n}}{n}$, où $X_{n}$ est la variable aléatoire définie à la question \textbf{2.}
	\begin{enumerate}
		\item Calculer l'espérance $E\left(F_{n}\right)$.

On admet que $V\left(F_{n}\right)=\dfrac{0,123975}{n}$.

		\item Vérifier que : $P\left(\left|F_{n}-0,145\right| \geqslant 0,1\right) \leqslant \dfrac{12,5}{n}$
		\item Un responsable de l'entreprise étudie un échantillon de \np{1000} connexions et constate que pour cet échantillon $F_{1000}=0,3$. II soupçonne un dysfonctionnement des serveurs. A-t-il raison ?
	\end{enumerate}
\end{enumerate}

\newpage

\textbf{\textsc{Exercice 2} \hfill 5 points}

\medskip

On considère la suite numérique $\left(u_{n}\right)$ définie par son premier terme $u_{0}=2$ et pour tout entier naturel $n$, par :


\[u_{n+1}=\dfrac{2 u_{n}+1}{u_{n}+2}\]

On admet que la suite $\left(u_{n}\right)$ est bien définie.

\begin{enumerate}
\item Calculer le terme $u_{1}$.
\item On définit la suite $\left(a_{n}\right)$ pour tout entier naturel $n$, par :

\[a_{n}=\dfrac{u_{n}}{u_{n}-1}\]

On admet que la suite $\left(a_{n}\right)$ est bien définie.

	\begin{enumerate}
		\item Calculer $a_{0}$ et $a_{1}$.

		\item Démontrer que, pour tout entier naturel $n$, $a_{n+1}=3 a_{n}-1$.

		\item Démontrer par récurrence que, pour tout entier naturel $n$ supérieur ou égal à~1,

\[a_{n} \geqslant 3 n-1\]

		\item En déduire la limite de la suite $\left(a_{n}\right)$.
	\end{enumerate}
\item On souhaite étudier la limite de la suite $\left(u_{n}\right)$.
	\begin{enumerate}
		\item Démontrer que pour tout entier naturel $n$, $u_{n}=\dfrac{a_{n}}{a_{n}-1}$.

		\item En déduire la limite de la suite $\left(u_{n}\right)$.
		\end{enumerate}
\item On admet que la suite $\left(u_{n}\right)$ est décroissante.

On considère le programme suivant écrit en langage Python :

\begin{center}
\begin{ttfamily}
\begin{tabularx}{8cm}{|l @{\quad} X|}\hline
	1& \textbf{def} algo(p):\\
	2& \quad u=2\\
	3& \quad n=0\\
	4& \quad \textbf{while} u-1>p:\\
	5& \quad\quad u=(2*u+1)/(u+2)\\
	6& \quad\quad n=n+1\\
	7& \quad \textbf{return} (n,u)\\ \hline
\end{tabularx}
\end{ttfamily}
\end{center}

	\begin{enumerate}
		\item Interpréter les valeurs \texttt{n} et \texttt{u} renvoyées par l'appel de la fonction \texttt{algo(p)} dans le contexte de l'exercice.
		\item Donner, sans justifier, la valeur de $n$ pour $p=0,001$.
	\end{enumerate}
\end{enumerate}

\newpage

\textbf{\textsc{Exercice 3} \hfill 4 points}

\medskip

\emph{Pour chacune des affirmations suivantes, indiquer si elle est vraie ou fausse. Chaque réponse doit être justifiée. Une réponse non justifiée ne rapporte aucun point.}

\medskip

L'espace est rapporté à un repère orthonormé \Oijk.

On considère la droite $(d)$ dont une représentation paramétrique est :


\[\left\{\begin{array}{l c l}
x&=&\phantom{-}3 - 2 t \\
y&=&-1 \\
z&=&\phantom{-}2 - 6 t
\end{array} \quad, \text { où } t \in \R\right.\]

On considère également les points suivants :

\begin{itemize}
\item A$(3~;~-3~;~-2)$
\item B$(5~;~-4~;~-1)$
\item C le point de la droite $(d)$ d'abscisse 2
\item H le projeté orthogonal du point B sur le plan $\mathcal{P}$ d'équation $x + 3z -7 = 0$
\end{itemize}

\medskip

\textbf{Affirmation 1}

La droite $(d)$ et l'axe des ordonnées sont deux droites non coplanaires.

\bigskip

\textbf{Affirmation 2}

Le plan passant par $A$ et orthogonal à la droite $(d)$ a pour équation cartésienne :

\[x + 3z + 3= 0\]

\bigskip

\textbf{Affirmation 3}

Une mesure, exprimée en radian, de l'angle géométrique $\widehat{\mathrm{BAC}}$ est $\dfrac{\pi}{6}$.

\bigskip

\textbf{Affirmation 4}

La distance BH est égale à $\dfrac{\sqrt{10}}{2}$.

\newpage

\textbf{\textsc{Exercice 4} \hfill 5 points}

\medskip

La \textbf{partie C} est indépendante des parties \textbf{A} et \textbf{B}.

\medskip

\textbf{Partie A}

\medskip

On donne ci-dessous, dans un repère orthogonal, les courbes $\mathcal{C}_{1}$ et $\mathcal{C}_{2}$, représentations graphiques de deux fonctions définies et dérivables sur $\R$. L'une des deux fonctions représentées est la fonction dérivée de l'autre. On les notera $g$ et $g’$.

On précise également que :
\begin{itemize}
\item La courbe $\mathcal{C}_{1}$ coupe l'axe des ordonnées au point de coordonnées $(0~;~1)$.
\item La courbe $\mathcal{C}_{2}$ coupe l'axe des ordonnées au point de coordonnées $(0~;~2)$ et l'axe des abscisses aux points de coordonnées $(-2~;~0)$ et $(1~;~0)$.
\end{itemize}

\begin{center}
\begin{tikzpicture}%compiler avec pgfplots
\begin{axis}[/pgf/number format/.cd, use comma,
x={15mm}, y={6mm}, %échelle originale : x=20mm
xmin=-3, xmax=5, ymin = -8, ymax= 7, %limites du graphique
xtick = {-2,...,4}, ytick={-7,...,6},
tick label style={font=\footnotesize},
minor tick num = 0, grid=major, axis lines =center]
\node[below left] at (axis cs: 0,0) {{\footnotesize 0}};
\addplot [line width=1.2pt, color=red, smooth, samples=300, domain= -3:5]{(x^2+3*x+1)*exp(-x)};
\node[red] at(axis cs: 2.2,2) {$ \mathcal{C}_1 $};
\addplot [line width=1.2pt, color=blue, dashed, smooth, samples=300, domain= -3:5]{(-x^2-x+2)*exp(-x)};
\node[blue] at(axis cs: 2.2,-1.1) {$ \mathcal{C}_2 $};
\end{axis}
\end{tikzpicture}

%%Version en pstricks:
%\psset{xunit=1.5cm,yunit=0.6cm} %sur le sujet original : xunit=2cm
%\begin{pspicture*}(-3.,-8.)(5.,7.)
%% Configuration de la grille et des axes
%\psgrid[gridlabels=0pt,subgriddiv=0,gridcolor=lightgray,gridwidth=0.25pt](0,0)(-3,-8)(5,7)
%%Axes
%\psaxes[linewidth=1.25pt,labelFontSize=\scriptstyle,ticks=xy]{->}(0,0)(-3,-8)(5,7)
%\uput[-135](0,0){\footnotesize 0}
%% Courbe C1 : (x^2+3*x+1)*exp(-x)
%\psplot[linewidth=1.2pt,linecolor=red,plotpoints=2000]{-3}{5}{x dup mul 3 x mul add 1 add 2.71828 x neg exp mul}
%\uput[0](2.2,2){\red $\mathcal{C}_1$}
%% Courbe C2 : (-x^2-x+2)*exp(-x)
%\psplot[linewidth=1.2pt,linecolor=blue,linestyle=dashed,plotpoints=2000]{-3}{5}{x dup mul neg x sub 2 add 2.71828 x neg exp mul}
%\uput[0](2.2,-1.1){\blue $\mathcal{C}_2$}
%\end{pspicture*}
\end{center}

\begin{enumerate}
	\item En justifiant, associer à chacune des fonctions $g$ et $g'$ sa représentation graphique.
	\item Justifier que l'équation réduite de la tangente à la
courbe représentative de la fonction $g$ au point d'abscisse 0 est $y=2x + 1$.
\end{enumerate}

\medskip

\textbf{Partie B}

\medskip

On considère $(E)$ l'équation différentielle 

\[y+y’=(2x + 3) \e^{-x},\]
où $y$ est une fonction de la variable réelle $x$.

\medskip

\begin{enumerate}
\item Montrer que la fonction $f_{0}$ définie pour tout nombre réel $x$ par $f_{0}(x)=\left(x^{2}+3 x\right) \e^{-x}$ est une solution particulière de l'équation différentielle $(E)$.
\item Résoudre l'équation différentielle $\left(E_{0}\right): y+ y'= 0$.
\item Déterminer les solutions de l'équation différentielle $(E)$.
\item On admet que la fonction $g$ décrite dans la \textbf{partie A} est une solution de l'équation différentielle $(E)$.

Déterminer alors l'expression de la fonction $g$.
\item Déterminer les solutions de l'équation différentielle $(E)$ dont la courbe admet exactement deux points d'inflexion.
\end{enumerate}

\medskip

\textbf{Partie C}

\medskip

On considère la fonction $f$ définie pour tout nombre réel $x$ par :

\[f(x)=\left(x^{2}+3 x+2\right) \e^{-x}\]

\begin{enumerate}
\item Démontrer que la limite de la fonction $f$ en $+\infty$ est égale à 0.

On admet par ailleurs que la limite de la fonction $f$ en $-\infty$ est égale à $+\infty$.

\item On admet que la fonction $f$ est dérivable sur $\R$. On note $f’$ la fonction dérivée de $f$ sur $\R$.
	\begin{enumerate}
		\item Vérifier que, pour tout nombre réel $x$, $f’(x)=\left(-x^{2}-x+1\right) \e^{-x}$.
		\item Déterminer le signe de la fonction dérivée $f’$ sur $\R$ puis en déduire les variations de la fonction $f$ sur $\R$.
	\end{enumerate}
\item Expliquer pourquoi la fonction $f$ est positive sur l'intervalle $[0;+\infty[$.
\item On notera $\mathcal{C}_{f}$ la courbe représentative de la fonction $f$ dans un repère orthogonal \Oij. On admet que la fonction $F$ définie pour tout nombre réel $x$ par $F(x)=\left(-x^{2}-5 x-7\right) \e^{-x}$ est une primitive de la fonction $f$.

Soit $\alpha$ un nombre réel positif.

Déterminer l'aire $\mathcal{A}(\alpha)$, exprimée en unité d'aire, du domaine du plan délimité par l'axe des abscisses, la courbe $\mathcal{C}_{f}$ et les droites d'équation $x=0$ et $x=\alpha$.
\end{enumerate}
\end{document}