import requests
from bs4 import BeautifulSoup
import os

def fetch_document(url, format_type='pdf'):
    """Fetches a document from URL. Supports PDF, HTML, TeX."""
    if not os.path.exists('downloads'):
        os.makedirs('downloads')
    file_path = f'downloads/{url.split("/")[-1]}'

    if format_type == 'pdf':
        response = requests.get(url)
        with open(file_path, 'wb') as f:
            f.write(response.content)
    elif format_type == 'html':
        response = requests.get(url)
        with open(file_path, 'w') as f:
            f.write(response.text)
    elif format_type == 'tex':
        response = requests.get(url)
        with open(file_path, 'w') as f:
            f.write(response.text)
    else:
        raise ValueError("Unsupported format")

    print(f"Explicit Output: Fetched {format_type} document to {file_path}")
    return file_path

def simple_crawler(start_url, max_docs=3):
    """Optional lightweight crawler to find exam links."""
    response = requests.get(start_url)
    soup = BeautifulSoup(response.text, 'html.parser')
    links = [a['href'] for a in soup.find_all('a') if 'exam' in a.text.lower()]
    fetched = []
    for link in links[:max_docs]:
        fetched.append(fetch_document(link))
    print(f"Explicit Output: Crawled and fetched {len(fetched)} documents")
    return fetched