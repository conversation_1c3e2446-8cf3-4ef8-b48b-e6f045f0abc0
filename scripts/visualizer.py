import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import os

def visualize_results(structured_data):
    if not os.path.exists('outputs/visuals'):
        os.makedirs('outputs/visuals')
    
    df = pd.DataFrame(structured_data)
    df['has_equation'] = df['equations'].apply(lambda x: len(x) > 0)
    df['has_table'] = df['tables'].apply(lambda x: len(x) > 0)
    
    # Bar chart: Question types distribution
    plt.figure()
    df['type'].value_counts().plot(kind='bar')
    plt.title('Question Types Distribution')
    plt.savefig('outputs/visuals/question_types_bar.png')
    
    # Pie chart: Format breakdown (simulated)
    formats = pd.Series(['PDF']*10 + ['HTML']*6 + ['TeX']*4)
    plt.figure()
    formats.value_counts().plot(kind='pie', autopct='%1.1f%%')
    plt.title('Document Formats Processed')
    plt.savefig('outputs/visuals/formats_pie.png')
    
    # Line plot: Simulated processing time vs. questions
    plt.figure()
    plt.plot(range(len(df)), range(len(df)), label='Time (s)')
    plt.title('Processing Time vs. Number of Questions')
    plt.savefig('outputs/visuals/time_line.png')
    
    # Heatmap: Accuracy per format/element (simulated)
    accuracy_data = pd.DataFrame({
        'Format': ['PDF', 'HTML', 'TeX'],
        'Equations': [0.95, 0.85, 0.90],
        'Tables': [0.80, 0.75, 0.85]
    }).set_index('Format')
    plt.figure()
    sns.heatmap(accuracy_data, annot=True)
    plt.title('Extraction Accuracy Heatmap')
    plt.savefig('outputs/visuals/accuracy_heatmap.png')
    
    print("Explicit Output: Generated 4 visualizations in outputs/visuals/")