import json
import re

def structure_data(raw_data):
    """Structures raw parsed data into JSON with LaTeX."""
    # First, clean and filter the raw data
    cleaned_data = _clean_and_filter_questions(raw_data)

    structured = []
    for item in cleaned_data:
        # Enhanced question type detection
        question_text = item["raw_question"].lower()
        answer_text = item.get("raw_answer", "").lower()

        # Determine question type with more sophisticated logic
        if (re.search(r"\([A-D]\)", item["raw_question"]) or
            re.search(r"[A-D][\.\)]\s", item["raw_question"]) or
            re.search(r"choices?", question_text) or
            re.search(r"select.*correct", question_text) or
            re.search(r"which.*following", question_text)):
            q_type = "multiple-choice"
        elif (re.search(r"true.*false|false.*true", question_text) or
              re.search(r"yes.*no|no.*yes", question_text) or
              re.search(r"true.*false", answer_text)):
            q_type = "true-false"
        elif (re.search(r"fill.*blank|complete.*sentence", question_text) or
              re.search(r"_+", item["raw_question"])):  # Blanks indicated by underscores
            q_type = "fill-in-blank"
        elif (re.search(r"match.*following|connect.*pairs", question_text)):
            q_type = "matching"
        elif (re.search(r"solve|calculate|find.*value|compute", question_text) and
              re.search(r"[\$\\].*[\$\\]", item["raw_question"])):  # Has math equations
            q_type = "calculation"
        elif (re.search(r"explain|describe|discuss|analyze|compare", question_text)):
            q_type = "essay"
        else:
            q_type = "open-ended"

        # Enhanced equation extraction
        equation_patterns = [
            r"[\$]{1,2}(.*?)[\$]{1,2}",  # LaTeX math mode
            r"\\begin\{equation\}(.*?)\\end\{equation\}",  # Equation environment
            r"\\begin\{align\}(.*?)\\end\{align\}",  # Align environment
            r"\\begin\{math\}(.*?)\\end\{math\}",  # Math environment
        ]

        equations = []
        full_text = item["raw_question"] + " " + item.get("raw_answer", "")
        for pattern in equation_patterns:
            matches = re.findall(pattern, full_text, re.DOTALL)
            equations.extend(matches)

        # Format equations properly
        formatted_equations = []
        for eq in equations:
            eq = eq.strip()
            if eq:  # Only add non-empty equations
                if len(eq) > 15 or re.search(r"\\[a-zA-Z]+", eq):  # Complex equation
                    formatted_equations.append(f"$$ {eq} $$")
                else:  # Simple equation
                    formatted_equations.append(f"\\( {eq} \\)")

        # Enhanced table detection
        tables = []
        if (re.search(r"table|chart|data.*show", question_text) or
            re.search(r"\\begin\{tabular\}", item["raw_question"]) or
            re.search(r"\\begin\{array\}", item["raw_question"])):
            # Try to extract actual table if present
            table_match = re.search(r"(\\begin\{tabular\}.*?\\end\{tabular\})",
                                  item["raw_question"], re.DOTALL)
            if table_match:
                tables.append(table_match.group(1))
            else:
                # Generate example table based on context
                if "grade" in question_text or "score" in question_text:
                    tables.append("\\begin{tabular}{c|c} Grade & Score \\\\ \\hline A & 90-100 \\\\ B & 80-89 \\end{tabular}")
                elif "time" in question_text or "hour" in question_text:
                    tables.append("\\begin{tabular}{c|c} Time & Value \\\\ \\hline 1 hr & 60 min \\\\ 2 hr & 120 min \\end{tabular}")
                else:
                    tables.append("\\begin{tabular}{c|c} Category & Value \\\\ \\hline Item 1 & Data 1 \\\\ Item 2 & Data 2 \\end{tabular}")

        # Enhanced figure detection
        if "figures" in item:  # From TeX parsing
            figures = item["figures"]  # Preserve raw TikZ code or reference
        else:
            figures = []
            if (re.search(r"figure|diagram|graph|chart|image", question_text) or
                re.search(r"\\begin\{tikzpicture\}", item["raw_question"]) or
                re.search(r"\\includegraphics", item["raw_question"])):
                # Try to extract actual figure code
                tikz_match = re.search(r"(\\begin\{tikzpicture\}.*?\\end\{tikzpicture\})",
                                     item["raw_question"], re.DOTALL)
                if tikz_match:
                    figures.append(tikz_match.group(1))
                else:
                    # Generate contextual placeholder
                    if "circle" in question_text:
                        figures.append("circle_diagram.png")
                    elif "triangle" in question_text:
                        figures.append("triangle_diagram.png")
                    elif "graph" in question_text:
                        figures.append("coordinate_graph.png")
                    else:
                        figures.append("diagram_placeholder.png")

        # Extract additional metadata
        difficulty = "medium"  # Default
        if (re.search(r"basic|simple|easy", question_text)):
            difficulty = "easy"
        elif (re.search(r"advanced|complex|difficult|challenging", question_text)):
            difficulty = "hard"

        # Extract subject/topic hints
        subject = "general"
        if re.search(r"math|algebra|geometry|calculus|equation", question_text):
            subject = "mathematics"
        elif re.search(r"chemistry|chemical|molecule|atom", question_text):
            subject = "chemistry"
        elif re.search(r"physics|force|energy|motion", question_text):
            subject = "physics"
        elif re.search(r"biology|cell|organism|dna", question_text):
            subject = "biology"
        elif re.search(r"history|historical|century|war", question_text):
            subject = "history"
        elif re.search(r"english|grammar|literature|writing", question_text):
            subject = "english"

        structured.append(
            {
                "question": item["raw_question"],
                "type": q_type,
                "answer": item.get("raw_answer", ""),
                "equations": formatted_equations,
                "tables": tables,
                "figures": figures,
                "difficulty": difficulty,
                "subject": subject,
                "metadata": {
                    "has_math": len(formatted_equations) > 0,
                    "has_table": len(tables) > 0,
                    "has_figure": len(figures) > 0,
                    "word_count": len(item["raw_question"].split())
                }
            }
        )

    with open("outputs/structured.json", "w") as f:
        json.dump(structured, f, indent=4)
    print(f"Explicit Output: Structured {len(structured)} questions into JSON")
    return structured

def _clean_and_filter_questions(raw_data):
    """Clean and filter raw question data to remove instructions and improve quality."""
    cleaned = []

    # Define instruction patterns to filter out
    instruction_patterns = [
        r'answers?\s+to\s+this\s+paper\s+must\s+be\s+written',
        r'time\s+is\s+to\s+be\s+spent\s+in\s+reading',
        r'time\s+given\s+at\s+the\s+head\s+of\s+this\s+paper',
        r'section\s+[A-Z]\s+is\s+compulsory',
        r'attempt\s+any\s+\w+\s+questions\s+from',
        r'attempt\s+all\s+questions\s+from\s+this\s+section',
        r'choose\s+the\s+correct\s+answers?\s+to\s+the\s+questions',
        r'do\s+not\s+copy\s+the\s+questions',
        r'write\s+the\s+correct\s+answers?\s+only',
        r'intended\s+marks\s+for\s+questions',
        r'marks\s+are\s+given\s+in\s+brackets',
        r'you\s+will\s+not\s+be\s+allowed\s+to\s+write',
        r'will\s+not\s+be\s+allowed\s+to\s+write',
        r'given\s+in\s+brackets?\s*\[',
        r'section-[A-Z]\s*\(\d+\s+marks\)',
        r'^\d+\.\s*[A-Z]\s*$',  # Single letter answers like "1. A"
        r'^\d+\.\s*(An?\s+\w+\s+reaction|Solution|Explanation)\s*$'  # Answer fragments
    ]

    # Process questions to merge related items and filter instructions
    i = 0
    while i < len(raw_data):
        current = raw_data[i]
        question_text = current["raw_question"].strip()

        # Skip if too short or looks like instruction
        if len(question_text) < 8:
            i += 1
            continue

        # Check if this is an instruction
        is_instruction = any(re.search(pattern, question_text.lower())
                           for pattern in instruction_patterns)

        if is_instruction:
            i += 1
            continue

        # Check if this is a standalone answer choice (A., B., C., D.)
        if re.match(r'^\d+\.\s*[A-D][\.\)]\s*[^A-D]*$', question_text):
            i += 1
            continue

        # Check if this is just a single word/phrase answer
        if (len(question_text.split()) <= 4 and
            not question_text.endswith('?') and
            not question_text.endswith('______.') and
            not re.search(r'_+', question_text)):
            i += 1
            continue

        # This looks like a valid question, check if we can improve it
        improved_question = _try_improve_question(raw_data, i)
        if improved_question:
            cleaned.append(improved_question["question"])
            i = improved_question["next_index"]
        else:
            cleaned.append(current)
            i += 1

    return cleaned

def _try_improve_question(raw_data, start_index):
    """Try to improve a question by merging with following answer choices or answers."""
    current = raw_data[start_index]
    question_text = current["raw_question"].strip()

    # If this is a fill-in-blank question, look for answer choices
    if (question_text.endswith('______.') or re.search(r'_+', question_text)):
        choices = []
        answer = ""
        j = start_index + 1

        # Look for answer choices in next few items
        while j < len(raw_data) and j < start_index + 8:
            next_item = raw_data[j]["raw_question"].strip()

            # Check for answer choice pattern
            choice_match = re.match(r'^\d+\.\s*([A-D])[\.\)]\s*(.+)$', next_item)
            if choice_match:
                choice_letter = choice_match.group(1)
                choice_text = choice_match.group(2).strip()
                if len(choice_text) > 2:  # Valid choice
                    choices.append(f"({choice_letter}) {choice_text}")
                j += 1
            # Check for solution/answer
            elif any(word in next_item.lower() for word in ['solution', 'answer', 'explanation']):
                if len(next_item) > 20:  # Substantial answer
                    answer = next_item
                j += 1
                break
            else:
                break

        # If we found choices, create multiple choice question
        if len(choices) >= 2:
            formatted_question = question_text + "\n" + "\n".join(choices)
            return {
                "question": {"raw_question": formatted_question, "raw_answer": answer},
                "next_index": j
            }

    # For regular questions, look for answer choices that follow
    elif not re.match(r'^\d+\.\s*[A-D][\.\)]', question_text):
        choices = []
        answer = ""
        j = start_index + 1

        # Look for answer choices
        while j < len(raw_data) and j < start_index + 6:
            next_item = raw_data[j]["raw_question"].strip()

            # Check for choice pattern
            choice_match = re.match(r'^\d+\.\s*([A-D])[\.\)]\s*(.+)$', next_item)
            if choice_match:
                choice_letter = choice_match.group(1)
                choice_text = choice_match.group(2).strip()
                if len(choice_text) > 2:
                    choices.append(f"({choice_letter}) {choice_text}")
                j += 1
            # Check for answer/solution
            elif any(word in next_item.lower() for word in ['solution', 'answer', 'explanation']):
                if len(next_item) > 15:
                    answer = next_item
                j += 1
                break
            else:
                break

        # If we found multiple choices, format as multiple choice
        if len(choices) >= 2:
            formatted_question = question_text + "\n" + "\n".join(choices)
            return {
                "question": {"raw_question": formatted_question, "raw_answer": answer},
                "next_index": j
            }

    return None