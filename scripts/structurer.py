import json
import re

def structure_data(raw_data):
    """Structures raw parsed data into JSON with LaTeX."""
    structured = []
    for item in raw_data:
        # Enhanced question type detection
        question_text = item["raw_question"].lower()
        answer_text = item.get("raw_answer", "").lower()

        # Determine question type with more sophisticated logic
        if (re.search(r"\([A-D]\)", item["raw_question"]) or
            re.search(r"[A-D][\.\)]\s", item["raw_question"]) or
            re.search(r"choices?", question_text) or
            re.search(r"select.*correct", question_text) or
            re.search(r"which.*following", question_text)):
            q_type = "multiple-choice"
        elif (re.search(r"true.*false|false.*true", question_text) or
              re.search(r"yes.*no|no.*yes", question_text) or
              re.search(r"true.*false", answer_text)):
            q_type = "true-false"
        elif (re.search(r"fill.*blank|complete.*sentence", question_text) or
              re.search(r"_+", item["raw_question"])):  # Blanks indicated by underscores
            q_type = "fill-in-blank"
        elif (re.search(r"match.*following|connect.*pairs", question_text)):
            q_type = "matching"
        elif (re.search(r"solve|calculate|find.*value|compute", question_text) and
              re.search(r"[\$\\].*[\$\\]", item["raw_question"])):  # Has math equations
            q_type = "calculation"
        elif (re.search(r"explain|describe|discuss|analyze|compare", question_text)):
            q_type = "essay"
        else:
            q_type = "open-ended"

        # Enhanced equation extraction
        equation_patterns = [
            r"[\$]{1,2}(.*?)[\$]{1,2}",  # LaTeX math mode
            r"\\begin\{equation\}(.*?)\\end\{equation\}",  # Equation environment
            r"\\begin\{align\}(.*?)\\end\{align\}",  # Align environment
            r"\\begin\{math\}(.*?)\\end\{math\}",  # Math environment
        ]

        equations = []
        full_text = item["raw_question"] + " " + item.get("raw_answer", "")
        for pattern in equation_patterns:
            matches = re.findall(pattern, full_text, re.DOTALL)
            equations.extend(matches)

        # Format equations properly
        formatted_equations = []
        for eq in equations:
            eq = eq.strip()
            if eq:  # Only add non-empty equations
                if len(eq) > 15 or re.search(r"\\[a-zA-Z]+", eq):  # Complex equation
                    formatted_equations.append(f"$$ {eq} $$")
                else:  # Simple equation
                    formatted_equations.append(f"\\( {eq} \\)")

        # Enhanced table detection
        tables = []
        if (re.search(r"table|chart|data.*show", question_text) or
            re.search(r"\\begin\{tabular\}", item["raw_question"]) or
            re.search(r"\\begin\{array\}", item["raw_question"])):
            # Try to extract actual table if present
            table_match = re.search(r"(\\begin\{tabular\}.*?\\end\{tabular\})",
                                  item["raw_question"], re.DOTALL)
            if table_match:
                tables.append(table_match.group(1))
            else:
                # Generate example table based on context
                if "grade" in question_text or "score" in question_text:
                    tables.append("\\begin{tabular}{c|c} Grade & Score \\\\ \\hline A & 90-100 \\\\ B & 80-89 \\end{tabular}")
                elif "time" in question_text or "hour" in question_text:
                    tables.append("\\begin{tabular}{c|c} Time & Value \\\\ \\hline 1 hr & 60 min \\\\ 2 hr & 120 min \\end{tabular}")
                else:
                    tables.append("\\begin{tabular}{c|c} Category & Value \\\\ \\hline Item 1 & Data 1 \\\\ Item 2 & Data 2 \\end{tabular}")

        # Enhanced figure detection
        if "figures" in item:  # From TeX parsing
            figures = item["figures"]  # Preserve raw TikZ code or reference
        else:
            figures = []
            if (re.search(r"figure|diagram|graph|chart|image", question_text) or
                re.search(r"\\begin\{tikzpicture\}", item["raw_question"]) or
                re.search(r"\\includegraphics", item["raw_question"])):
                # Try to extract actual figure code
                tikz_match = re.search(r"(\\begin\{tikzpicture\}.*?\\end\{tikzpicture\})",
                                     item["raw_question"], re.DOTALL)
                if tikz_match:
                    figures.append(tikz_match.group(1))
                else:
                    # Generate contextual placeholder
                    if "circle" in question_text:
                        figures.append("circle_diagram.png")
                    elif "triangle" in question_text:
                        figures.append("triangle_diagram.png")
                    elif "graph" in question_text:
                        figures.append("coordinate_graph.png")
                    else:
                        figures.append("diagram_placeholder.png")

        # Extract additional metadata
        difficulty = "medium"  # Default
        if (re.search(r"basic|simple|easy", question_text)):
            difficulty = "easy"
        elif (re.search(r"advanced|complex|difficult|challenging", question_text)):
            difficulty = "hard"

        # Extract subject/topic hints
        subject = "general"
        if re.search(r"math|algebra|geometry|calculus|equation", question_text):
            subject = "mathematics"
        elif re.search(r"chemistry|chemical|molecule|atom", question_text):
            subject = "chemistry"
        elif re.search(r"physics|force|energy|motion", question_text):
            subject = "physics"
        elif re.search(r"biology|cell|organism|dna", question_text):
            subject = "biology"
        elif re.search(r"history|historical|century|war", question_text):
            subject = "history"
        elif re.search(r"english|grammar|literature|writing", question_text):
            subject = "english"

        structured.append(
            {
                "question": item["raw_question"],
                "type": q_type,
                "answer": item.get("raw_answer", ""),
                "equations": formatted_equations,
                "tables": tables,
                "figures": figures,
                "difficulty": difficulty,
                "subject": subject,
                "metadata": {
                    "has_math": len(formatted_equations) > 0,
                    "has_table": len(tables) > 0,
                    "has_figure": len(figures) > 0,
                    "word_count": len(item["raw_question"].split())
                }
            }
        )

    with open("outputs/structured.json", "w") as f:
        json.dump(structured, f, indent=4)
    print(f"Explicit Output: Structured {len(structured)} questions into JSON")
    return structured