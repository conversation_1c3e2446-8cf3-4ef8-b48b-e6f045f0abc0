import json
import re

def structure_data(raw_data):
    """Structures raw parsed data into JSON with LaTeX."""
    structured = []
    for item in raw_data:
        q_type = (
            "multiple-choice"
            if re.search(r"\(A\)|choices", item["raw_question"])
            else "open-ended"
        )
        equations = re.findall(
            r"[\$]{1,2}(.*?)[\$]{1,2}",
            item["raw_question"] + item["raw_answer"],
        )
        equations = [
            f"$$ {eq} $$" if len(eq) > 10 else f"\\( {eq} \\)" for eq in equations
        ]
        tables = (
            [
                "\\begin{tabular}{c|c} Example & Table \\\\ \\hline 1 & 2 \\end{tabular}"
            ]
            if "table" in item["raw_question"].lower()
            else []
        )
        if "figures" in item:  # From TeX parsing
            figures = item["figures"]  # Preserve raw TikZ code or reference
        else:
            figures = (
                ["placeholder_figure.png"]
                if "figure" in item["raw_question"].lower()
                else []
            )
        structured.append(
            {
                "question": item["raw_question"],
                "type": q_type,
                "answer": item["raw_answer"],
                "equations": equations,
                "tables": tables,
                "figures": figures,
            }
        )
    with open("outputs/structured.json", "w") as f:
        json.dump(structured, f, indent=4)
    print(f"Explicit Output: Structured {len(structured)} questions into JSON")
    return structured