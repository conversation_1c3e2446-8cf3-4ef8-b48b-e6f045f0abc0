import os
from fetcher import fetch_document, simple_crawler
from parser import parse_document
from structurer import structure_data
from visualizer import visualize_results

def main():
    if not os.path.exists('outputs'):
        os.makedirs('outputs')
    if not os.path.exists('downloads'):
        os.makedirs('downloads')
    
    # Define your documents here (mix of local paths and URLs, with format)
    docs_to_process = [
        ('data/chem_exam.pdf', 'pdf'),  # Local PDF (chemistry exam)
        ('data/math_exam.tex', 'tex'),  # Local TeX (math exam) - Corrected filename
        ('https://www.easy-quizzz.com/us/academic-test/admission-test/teas-practice-test/quiz.html', 'html')  # HTML URL (TEAS quiz)
    ]
    
    all_raw_data = []
    
    # Fetch and parse
    for url_or_path, format_type in docs_to_process:
        if url_or_path.startswith('http'):  # Fetch from web
            file_path = fetch_document(url_or_path, format_type)
        else:  # Local file
            file_path = url_or_path
            print(f"Explicit Output: Using local file {file_path}")
        raw_data = parse_document(file_path, format_type)
        if raw_data:
            all_raw_data.extend(raw_data)  # Include all questions to demonstrate robustness
            print(f"Added {len(raw_data)} questions from {file_path}")
    
    # Structure and visualize
    if all_raw_data:
        structured = structure_data(all_raw_data)
        visualize_results(structured)
    else:
        print("No data parsed. Check document formats or URLs.")
    
    print(f"Explicit Output: Processed {len(all_raw_data)} raw questions from documents. Pipeline complete.")

if __name__ == '__main__':
    main()