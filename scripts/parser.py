import pdfplumber
from bs4 import BeautifulSoup
import textract
import re
import nltk
import os
nltk.download('punkt', quiet=True)
import pytesseract
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

# In parser.py (replace the text extraction and matching logic inside the with block)
def parse_pdf(file_path):
    """Parses PDF for questions, answers, etc."""
    questions = []
    with pdfplumber.open(file_path) as pdf:
        text = ""
        for page in pdf.pages:
            text += page.extract_text() or ''
        
        # Look for questions with various patterns
        patterns = [
            r'(\d+[\.\)]\s*.*?)(?=\d+[\.\)]|\Z)',  # Numbered questions
            r'(Question\s+\d+.*?)(?=Question\s+\d+|\Z)',  # Questions prefixed with "Question"
            r'([A-Z]\.\s*.*?)(?=[A-Z]\.\s*|\Z)'  # Lettered questions
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.DOTALL | re.MULTILINE)
            for match in matches:
                question_text = match.strip()
                if len(question_text) > 10:  # Filter out too short matches
                    questions.append({"raw_question": question_text, "raw_answer": ""})
                    
    print(f"Explicit Output: Parsed {len(questions)} items from PDF")
    return questions

def parse_html(file_path):
    """Parses HTML for questions."""
    # Set up headless Selenium for dynamic content
    options = Options()
    options.headless = True  # Run without opening browser window
    driver = webdriver.Chrome(options=options)
    
    # Load the page (file_path could be local file or URL)
    if file_path.startswith('http'):
        driver.get(file_path)
    else:
        driver.get('file://' + os.path.abspath(file_path))
    
    # Get rendered source and parse with BeautifulSoup
    soup = BeautifulSoup(driver.page_source, 'html.parser')
    driver.quit()  # Close the browser
    
    questions = []
    # Updated to target common quiz tags (inspect page and adjust classes)
    for div in soup.find_all('div', class_='quiz-question'):  # Adjust class as needed
        q = div.find('p', class_='question-text').text if div.find('p', class_='question-text') else ''
        a = div.find('p', class_='answer-text').text if div.find('p', class_='answer-text') else ''
        if q:  # Only add if question found
            questions.append({"raw_question": q, "raw_answer": a})
    
    print(f"Explicit Output: Parsed {len(questions)} items from HTML (dynamic fetch)")
    return questions

def parse_tex(file_path):
    """Parses TeX/LaTeX documents."""
    with open(file_path, 'r') as f:
        text = f.read()
    
    questions = []
    
    # Look for question environments
    env_matches = re.findall(r'\\begin\{question\}(.*?)\\end\{question\}', 
                           text, re.DOTALL)
    
    # If no question environments found, try numbered questions
    if not env_matches:
        # Split by question numbers
        matches = re.split(r'(\d+\.|[A-Za-z]\))\s+', text)
        for i in range(1, len(matches)-1, 2):
            question_num = matches[i]
            question_text = matches[i+1]
            if len(question_text.strip()) > 10:
                questions.append({
                    "raw_question": f"{question_num} {question_text}".strip(),
                    "raw_answer": ""
                })
    else:
        for q_text in env_matches:
            # Extract answer if exists
            answer = ""
            ans_match = re.search(r'\\begin\{answer\}(.*?)\\end\{answer\}', 
                               q_text, re.DOTALL)
            if ans_match:
                answer = ans_match.group(1).strip()
                q_text = q_text.replace(ans_match.group(0), '').strip()
            
            questions.append({
                "raw_question": q_text.strip(),
                "raw_answer": answer
            })

def parse_document(file_path, format_type):
    if format_type == 'pdf':
        return parse_pdf(file_path)
    elif format_type == 'html':
        return parse_html(file_path)
    elif format_type == 'tex':
        return parse_tex(file_path)
    raise ValueError("Unsupported format")