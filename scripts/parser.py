import pdfplumber
from bs4 import BeautifulSoup
import textract
import re
import nltk
import os
nltk.download('punkt', quiet=True)
import pytesseract
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

# In parser.py (replace the text extraction and matching logic inside the with block)
def parse_pdf(file_path):
    """Parses PDF for questions, answers, etc."""
    questions = []
    with pdfplumber.open(file_path) as pdf:
        text = ""
        for page in pdf.pages:
            text += page.extract_text() or ''

        # Clean up text
        text = re.sub(r'\s+', ' ', text)  # Normalize whitespace

        # Filter out common instruction patterns first
        instruction_patterns = [
            r'answers?\s+to\s+this\s+paper\s+must\s+be\s+written',
            r'time\s+is\s+to\s+be\s+spent\s+in\s+reading',
            r'time\s+given\s+at\s+the\s+head\s+of\s+this\s+paper',
            r'section\s+[A-Z]\s+is\s+compulsory',
            r'attempt\s+any\s+\w+\s+questions\s+from',
            r'choose\s+the\s+correct\s+answers?\s+to\s+the\s+questions',
            r'do\s+not\s+copy\s+the\s+questions',
            r'write\s+the\s+correct\s+answers?\s+only'
        ]

        # Try to find complete question-answer pairs with better grouping
        # First, split text into potential question blocks
        question_blocks = re.split(r'(\d+[\.\)]\s+)', text)

        i = 1  # Skip first empty element
        while i < len(question_blocks):
            if i + 1 < len(question_blocks):
                question_num = question_blocks[i].strip()
                question_content = question_blocks[i + 1].strip()

                # Skip if this looks like an instruction
                is_instruction = any(re.search(pattern, question_content.lower())
                                   for pattern in instruction_patterns)

                if not is_instruction and len(question_content) > 15:
                    # Check if this contains multiple choice options
                    if re.search(r'\([A-D]\)', question_content):
                        # Extract the main question part and choices
                        parts = re.split(r'(\([A-D]\)[^(]*?)(?=\([A-D]\)|\Z)', question_content)
                        main_question = parts[0].strip()

                        # Extract choices
                        choices = []
                        for j in range(1, len(parts), 2):
                            if j < len(parts):
                                choice = parts[j].strip()
                                if choice and re.match(r'\([A-D]\)', choice):
                                    choices.append(choice)

                        if len(choices) >= 2:  # Valid multiple choice
                            formatted_question = f"{question_num}{main_question}\n" + "\n".join(choices)

                            # Look ahead for answer/solution
                            answer = ""
                            if i + 3 < len(question_blocks):
                                next_content = question_blocks[i + 3].lower()
                                if 'solution' in next_content or 'answer' in next_content or 'explanation' in next_content:
                                    answer = question_blocks[i + 3].strip()

                            questions.append({"raw_question": formatted_question, "raw_answer": answer})

                    # Check if this is a fill-in-the-blank question
                    elif re.search(r'_+', question_content) or question_content.endswith('______.'):
                        formatted_question = f"{question_num}{question_content}"

                        # Look for answer in subsequent blocks
                        answer = ""
                        for j in range(i + 2, min(i + 6, len(question_blocks)), 2):
                            if j < len(question_blocks):
                                potential_answer = question_blocks[j].strip()
                                # Check if this looks like an answer (short, not another question)
                                if (len(potential_answer) < 50 and
                                    not re.match(r'\d+[\.\)]', potential_answer) and
                                    not any(re.search(pattern, potential_answer.lower())
                                           for pattern in instruction_patterns)):
                                    answer = potential_answer
                                    break

                        questions.append({"raw_question": formatted_question, "raw_answer": answer})

                    # Regular question
                    elif not re.match(r'^[A-D][\.\)]\s', question_content):  # Not a standalone choice
                        formatted_question = f"{question_num}{question_content}"
                        questions.append({"raw_question": formatted_question, "raw_answer": ""})

            i += 2  # Move to next question number

        # If no structured questions found, fall back to simpler parsing
        if not questions:
            simple_patterns = [
                r'(\d+[\.\)]\s*[^0-9]{15,}?)(?=\d+[\.\)]|\Z)',  # Numbered questions (min 15 chars)
            ]

            for pattern in simple_patterns:
                matches = re.findall(pattern, text, re.DOTALL | re.MULTILINE)
                for match in matches:
                    question_text = match.strip()
                    # Filter out instructions
                    is_instruction = any(re.search(pattern, question_text.lower())
                                       for pattern in instruction_patterns)
                    if not is_instruction:
                        questions.append({"raw_question": question_text, "raw_answer": ""})

        # Post-process to clean up and merge related questions
        questions = _post_process_pdf_questions(questions)

    print(f"Explicit Output: Parsed {len(questions)} items from PDF")
    return questions

def _post_process_pdf_questions(raw_questions):
    """Post-process PDF questions to merge answers with questions and filter out non-questions"""
    processed = []
    i = 0

    while i < len(raw_questions):
        current = raw_questions[i]
        question_text = current["raw_question"].strip()

        # Skip if this looks like an instruction or answer choice fragment
        if (len(question_text) < 10 or
            re.match(r'^[A-D][\.\)]\s', question_text) or  # Standalone choice
            re.match(r'^\d+[\.\)]\s*(An?\s+\w+\s+reaction|Solution|Explanation)', question_text) or  # Answer fragment
            'must be written' in question_text.lower() or
            'time is to be spent' in question_text.lower()):
            i += 1
            continue

        # Check if this is a fill-in-blank question followed by answer choices
        if (question_text.endswith('______.') or re.search(r'_+', question_text)):
            # Look for answer choices in the next few items
            choices = []
            answer = ""
            j = i + 1

            while j < len(raw_questions) and j < i + 6:
                next_item = raw_questions[j]["raw_question"].strip()

                # Check if this is an answer choice (A., B., etc.)
                if re.match(r'^[A-D][\.\)]\s', next_item):
                    choice_letter = next_item[0]
                    choice_text = re.sub(r'^[A-D][\.\)]\s*', '', next_item).strip()
                    choices.append(f"({choice_letter}) {choice_text}")
                    j += 1
                # Check if this is a solution/answer
                elif ('solution' in next_item.lower() or 'explanation' in next_item.lower()):
                    answer = next_item
                    j += 1
                    break
                else:
                    break

            # If we found choices, format as multiple choice
            if len(choices) >= 2:
                formatted_question = question_text + "\n" + "\n".join(choices)
                processed.append({"raw_question": formatted_question, "raw_answer": answer})
                i = j  # Skip the processed items
            else:
                processed.append(current)
                i += 1

        # Check if this is a regular question that might have answer choices following
        elif not re.match(r'^[A-D][\.\)]\s', question_text):
            # Look ahead for potential answer choices
            choices = []
            answer = ""
            j = i + 1

            # Check next few items for answer choices
            while j < len(raw_questions) and j < i + 6:
                next_item = raw_questions[j]["raw_question"].strip()

                if re.match(r'^[A-D][\.\)]\s', next_item):
                    choice_letter = next_item[0]
                    choice_text = re.sub(r'^[A-D][\.\)]\s*', '', next_item).strip()
                    if len(choice_text) > 3:  # Valid choice text
                        choices.append(f"({choice_letter}) {choice_text}")
                    j += 1
                elif ('solution' in next_item.lower() or 'explanation' in next_item.lower()):
                    answer = next_item
                    j += 1
                    break
                else:
                    break

            # If we found multiple choices, format as multiple choice
            if len(choices) >= 2:
                formatted_question = question_text + "\n" + "\n".join(choices)
                processed.append({"raw_question": formatted_question, "raw_answer": answer})
                i = j  # Skip the processed items
            else:
                processed.append(current)
                i += 1
        else:
            i += 1  # Skip standalone choices that weren't merged

    return processed

def parse_html(file_path):
    """Parses HTML for questions."""
    # Set up headless Selenium for dynamic content
    options = Options()
    options.headless = True  # Run without opening browser window
    driver = webdriver.Chrome(options=options)

    # Load the page (file_path could be local file or URL)
    if file_path.startswith('http'):
        driver.get(file_path)
    else:
        driver.get('file://' + os.path.abspath(file_path))

    # Get rendered source and parse with BeautifulSoup
    soup = BeautifulSoup(driver.page_source, 'html.parser')
    driver.quit()  # Close the browser

    questions = []

    # First try to extract from JSON-LD structured data
    json_scripts = soup.find_all('script', type='application/ld+json')
    for script in json_scripts:
        try:
            import json
            data = json.loads(script.string)

            # Handle different JSON-LD structures
            if isinstance(data, dict):
                # Look for @graph structure
                if '@graph' in data:
                    for item in data['@graph']:
                        if item.get('@type') == 'Quiz' and 'hasPart' in item:
                            for question_data in item['hasPart']:
                                if question_data.get('@type') == 'Question':
                                    questions.extend(_extract_question_from_jsonld(question_data))
                # Direct Quiz structure
                elif data.get('@type') == 'Quiz' and 'hasPart' in data:
                    for question_data in data['hasPart']:
                        if question_data.get('@type') == 'Question':
                            questions.extend(_extract_question_from_jsonld(question_data))
            elif isinstance(data, list):
                for item in data:
                    if item.get('@type') == 'Quiz' and 'hasPart' in item:
                        for question_data in item['hasPart']:
                            if question_data.get('@type') == 'Question':
                                questions.extend(_extract_question_from_jsonld(question_data))
        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"Warning: Could not parse JSON-LD data: {e}")
            continue

    # Fallback: try traditional HTML parsing if no JSON-LD found
    if not questions:
        # Try multiple common quiz HTML structures
        selectors = [
            ('div', 'quiz-question'),
            ('div', 'question'),
            ('div', 'mcq'),
            ('li', 'question-item'),
            ('div', 'test-question')
        ]

        for tag, class_name in selectors:
            divs = soup.find_all(tag, class_=class_name)
            for div in divs:
                q_selectors = ['p.question-text', '.question-text', 'h3', 'h4', '.question']
                a_selectors = ['p.answer-text', '.answer-text', '.correct-answer', '.answer']

                q = ''
                a = ''

                for q_sel in q_selectors:
                    q_elem = div.select_one(q_sel)
                    if q_elem:
                        q = q_elem.get_text(strip=True)
                        break

                for a_sel in a_selectors:
                    a_elem = div.select_one(a_sel)
                    if a_elem:
                        a = a_elem.get_text(strip=True)
                        break

                if q:
                    questions.append({"raw_question": q, "raw_answer": a})

            if questions:  # Stop if we found questions with this selector
                break

    print(f"Explicit Output: Parsed {len(questions)} items from HTML (dynamic fetch)")
    return questions

def _extract_question_from_jsonld(question_data):
    """Extract question data from JSON-LD Question object"""
    questions = []

    question_text = question_data.get('text', '')
    question_name = question_data.get('name', '')

    # Combine name and text for full question
    full_question = f"{question_name}: {question_text}" if question_name else question_text

    # Extract answer choices and correct answer
    suggested_answers = question_data.get('suggestedAnswer', [])
    accepted_answers = question_data.get('acceptedAnswer', [])

    if suggested_answers:
        # This is a multiple choice question
        choices = []
        correct_answer = ""

        for i, answer in enumerate(suggested_answers):
            choice_text = answer.get('text', '')
            choice_letter = chr(65 + i)  # A, B, C, D...
            choices.append(f"({choice_letter}) {choice_text}")

        # Find correct answer
        if accepted_answers:
            correct_pos = accepted_answers[0].get('position', 0)
            correct_letter = chr(65 + correct_pos)
            correct_answer = f"{correct_letter}. {accepted_answers[0].get('text', '')}"

            # Add explanation if available
            explanation = accepted_answers[0].get('answerExplanation', {})
            if explanation and explanation.get('text'):
                correct_answer += f" - {explanation['text']}"

        # Format as multiple choice
        mc_question = f"{full_question}\n" + "\n".join(choices)
        questions.append({"raw_question": mc_question, "raw_answer": correct_answer})
    else:
        # Open-ended question
        answer = ""
        if accepted_answers:
            answer = accepted_answers[0].get('text', '')
            explanation = accepted_answers[0].get('answerExplanation', {})
            if explanation and explanation.get('text'):
                answer += f" - {explanation['text']}"

        questions.append({"raw_question": full_question, "raw_answer": answer})

    return questions

def parse_tex(file_path):
    """Parses TeX/LaTeX documents."""
    with open(file_path, 'r') as f:
        text = f.read()

    questions = []

    # Look for question environments first
    env_matches = re.findall(r'\\begin\{question\}(.*?)\\end\{question\}',
                           text, re.DOTALL)

    if env_matches:
        for q_text in env_matches:
            # Extract answer if exists
            answer = ""
            ans_match = re.search(r'\\begin\{answer\}(.*?)\\end\{answer\}',
                               q_text, re.DOTALL)
            if ans_match:
                answer = ans_match.group(1).strip()
                q_text = q_text.replace(ans_match.group(0), '').strip()

            # Extract choices if it's multiple choice
            choice_matches = re.findall(r'\\choice\s*\{([^}]*)\}', q_text)
            if choice_matches:
                # Format as multiple choice
                question_part = re.sub(r'\\choice\s*\{[^}]*\}', '', q_text).strip()
                choices = [f"({chr(65+i)}) {choice}" for i, choice in enumerate(choice_matches)]
                formatted_question = question_part + "\n" + "\n".join(choices)
                questions.append({
                    "raw_question": formatted_question,
                    "raw_answer": answer
                })
            else:
                questions.append({
                    "raw_question": q_text.strip(),
                    "raw_answer": answer
                })

    # Look for problem environments
    if not questions:
        prob_matches = re.findall(r'\\begin\{problem\}(.*?)\\end\{problem\}',
                               text, re.DOTALL)
        for p_text in prob_matches:
            # Extract solution if exists
            solution = ""
            sol_match = re.search(r'\\begin\{solution\}(.*?)\\end\{solution\}',
                               p_text, re.DOTALL)
            if sol_match:
                solution = sol_match.group(1).strip()
                p_text = p_text.replace(sol_match.group(0), '').strip()

            questions.append({
                "raw_question": p_text.strip(),
                "raw_answer": solution
            })

    # Fallback: try numbered questions
    if not questions:
        # Split by question numbers - improved pattern
        patterns = [
            r'(\d+[\.\)]\s*[^0-9]+?)(?=\d+[\.\)]|\Z)',  # Numbered questions
            r'(Problem\s+\d+[^P]*?)(?=Problem\s+\d+|\Z)',  # Problem format
            r'(Exercise\s+\d+[^E]*?)(?=Exercise\s+\d+|\Z)'  # Exercise format
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.DOTALL | re.MULTILINE)
            for match in matches:
                question_text = match.strip()
                if len(question_text) > 15:  # Filter out too short matches
                    # Look for multiple choice indicators
                    if re.search(r'\([A-D]\)', question_text) or re.search(r'[A-D][\.\)]\s', question_text):
                        # Try to extract and format choices
                        choices = re.findall(r'([A-D][\.\)]\s*[^A-D\n]*)', question_text)
                        if len(choices) >= 2:
                            question_part = re.split(r'[A-D][\.\)]', question_text)[0].strip()
                            formatted_choices = [f"({choice[0]}) {choice[2:].strip()}" for choice in choices]
                            formatted_question = question_part + "\n" + "\n".join(formatted_choices)
                            questions.append({
                                "raw_question": formatted_question,
                                "raw_answer": ""
                            })
                        else:
                            questions.append({
                                "raw_question": question_text,
                                "raw_answer": ""
                            })
                    else:
                        questions.append({
                            "raw_question": question_text,
                            "raw_answer": ""
                        })

            if questions:  # Stop if we found questions with this pattern
                break

    print(f"Explicit Output: Parsed {len(questions)} items from TeX")
    return questions

def parse_document(file_path, format_type):
    if format_type == 'pdf':
        return parse_pdf(file_path)
    elif format_type == 'html':
        return parse_html(file_path)
    elif format_type == 'tex':
        return parse_tex(file_path)
    raise ValueError("Unsupported format")