import unittest
import tempfile
import os
import json
import shutil
from unittest.mock import patch, MagicMock, mock_open
from scripts.parser import parse_pdf, parse_html, parse_tex, parse_document
from scripts.structurer import structure_data
from scripts.fetcher import fetch_document, simple_crawler
from scripts.visualizer import visualize_results
import requests
from selenium.common.exceptions import WebDriverException, TimeoutException


class TestPDFParserEdgeCases(unittest.TestCase):
    """Test edge cases for PDF parsing functionality"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_pdf_path = os.path.join(self.temp_dir, 'test.pdf')

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_pdf_parsing_basic(self):
        """Test basic PDF parsing functionality"""
        # Create temp file for testing
        with open(self.test_pdf_path, 'w') as f:
            f.write("Question 1: What is Python? \nAnswer: A programming language")
        parsed = parse_pdf(self.test_pdf_path)
        self.assertIsInstance(parsed, list)

    def test_pdf_empty_file(self):
        """Test parsing empty PDF file"""
        with open(self.test_pdf_path, 'w') as f:
            f.write("")
        parsed = parse_pdf(self.test_pdf_path)
        self.assertEqual(len(parsed), 0)

    def test_pdf_no_questions(self):
        """Test PDF with no recognizable question patterns"""
        with open(self.test_pdf_path, 'w') as f:
            f.write("This is just random text without any questions or answers.")
        parsed = parse_pdf(self.test_pdf_path)
        self.assertEqual(len(parsed), 0)

    def test_pdf_special_characters(self):
        """Test PDF with special characters and encoding issues"""
        with open(self.test_pdf_path, 'w', encoding='utf-8') as f:
            f.write("Question 1: What is α + β? \nAnswer: Greek letters")
        parsed = parse_pdf(self.test_pdf_path)
        self.assertIsInstance(parsed, list)

    def test_pdf_multiple_questions(self):
        """Test PDF with multiple questions"""
        content = """Question 1: What is 2+2?
Answer: 4
Question 2: What is the capital of France?
Answer: Paris
Question 3: What is Python?
Answer: Programming language"""
        with open(self.test_pdf_path, 'w') as f:
            f.write(content)
        parsed = parse_pdf(self.test_pdf_path)
        self.assertGreaterEqual(len(parsed), 0)  # Should handle multiple questions

    @patch('scripts.parser.pdfplumber.open')
    def test_pdf_pdfplumber_exception(self, mock_pdfplumber):
        """Test handling of pdfplumber exceptions"""
        mock_pdfplumber.side_effect = Exception("PDF reading error")
        with self.assertRaises(Exception):
            parse_pdf(self.test_pdf_path)

    def test_pdf_nonexistent_file(self):
        """Test parsing non-existent PDF file"""
        with self.assertRaises(FileNotFoundError):
            parse_pdf("nonexistent.pdf")

    @patch('scripts.parser.pytesseract.image_to_string')
    @patch('scripts.parser.pdfplumber.open')
    def test_pdf_ocr_fallback(self, mock_pdfplumber, mock_ocr):
        """Test OCR fallback for scanned PDFs"""
        # Mock pdfplumber to return empty text (simulating scanned PDF)
        mock_page = MagicMock()
        mock_page.extract_text.return_value = ""
        mock_page.to_image.return_value.original = MagicMock()

        mock_pdf = MagicMock()
        mock_pdf.pages = [mock_page]
        mock_pdfplumber.return_value.__enter__.return_value = mock_pdf

        # Mock OCR to return text
        mock_ocr.return_value = "Question 1: OCR extracted text\nAnswer: OCR works"

        parsed = parse_pdf(self.test_pdf_path)
        mock_ocr.assert_called_once()
        self.assertIsInstance(parsed, list)


class TestTeXParserEdgeCases(unittest.TestCase):
    """Test edge cases for TeX parsing functionality"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_tex_path = os.path.join(self.temp_dir, 'test.tex')

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_tex_basic_parsing(self):
        """Test basic TeX parsing"""
        content = r"\question{What is LaTeX?}\answer{A typesetting system}"
        with open(self.test_tex_path, 'w', encoding='utf-8') as f:
            f.write(content)
        parsed = parse_tex(self.test_tex_path)
        self.assertIsInstance(parsed, list)

    def test_tex_empty_file(self):
        """Test parsing empty TeX file"""
        with open(self.test_tex_path, 'w') as f:
            f.write("")
        parsed = parse_tex(self.test_tex_path)
        self.assertEqual(len(parsed), 0)

    def test_tex_french_characters(self):
        """Test TeX with French characters and accents"""
        content = r"\question{Qu'est-ce que c'est?}\answer{C'est français avec des accents: é, à, ç}"
        with open(self.test_tex_path, 'w', encoding='utf-8') as f:
            f.write(content)
        parsed = parse_tex(self.test_tex_path)
        self.assertIsInstance(parsed, list)

    def test_tex_tikz_figures(self):
        """Test TeX with TikZ figures"""
        content = r"""
        \question{What is this figure?}
        \answer{A tree diagram}
        \begin{tikzpicture}
        \node (A) at (0,0) {A};
        \node (B) at (1,1) {B};
        \draw (A) -- (B);
        \end{tikzpicture}
        """
        with open(self.test_tex_path, 'w', encoding='utf-8') as f:
            f.write(content)
        parsed = parse_tex(self.test_tex_path)
        self.assertIsInstance(parsed, list)
        # Should detect TikZ figures
        has_figures = any('figures' in item for item in parsed)
        self.assertTrue(has_figures or len(parsed) > 0)

    def test_tex_complex_math(self):
        """Test TeX with complex mathematical notation"""
        content = r"""
        \question{What is $\int_0^\infty e^{-x^2} dx$?}
        \answer{$\frac{\sqrt{\pi}}{2}$}
        """
        with open(self.test_tex_path, 'w', encoding='utf-8') as f:
            f.write(content)
        parsed = parse_tex(self.test_tex_path)
        self.assertIsInstance(parsed, list)

    @patch('scripts.parser.textract.process')
    def test_tex_textract_exception(self, mock_textract):
        """Test handling of textract exceptions"""
        mock_textract.side_effect = Exception("Textract error")
        with self.assertRaises(Exception):
            parse_tex(self.test_tex_path)

    def test_tex_malformed_syntax(self):
        """Test TeX with malformed syntax"""
        content = r"\question{Incomplete \answer{Missing closing brace"
        with open(self.test_tex_path, 'w', encoding='utf-8') as f:
            f.write(content)
        parsed = parse_tex(self.test_tex_path)
        # Should handle gracefully without crashing
        self.assertIsInstance(parsed, list)

    def test_tex_no_questions(self):
        """Test TeX file with no question/answer patterns"""
        content = r"""
        \documentclass{article}
        \begin{document}
        This is just regular LaTeX text without questions.
        \end{document}
        """
        with open(self.test_tex_path, 'w', encoding='utf-8') as f:
            f.write(content)
        parsed = parse_tex(self.test_tex_path)
        self.assertEqual(len(parsed), 0)


class TestHTMLParserEdgeCases(unittest.TestCase):
    """Test edge cases for HTML parsing functionality"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_html_path = os.path.join(self.temp_dir, 'test.html')

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_html_basic_parsing(self):
        """Test basic HTML parsing"""
        content = """
        <html>
        <body>
        <div class="quiz-question">
            <p class="question-text">What is HTML?</p>
            <p class="answer-text">HyperText Markup Language</p>
        </div>
        </body>
        </html>
        """
        with open(self.test_html_path, 'w', encoding='utf-8') as f:
            f.write(content)

        with patch('scripts.parser.webdriver.Chrome') as mock_driver:
            mock_driver_instance = MagicMock()
            mock_driver.return_value = mock_driver_instance
            mock_driver_instance.page_source = content

            parsed = parse_html(self.test_html_path)
            self.assertIsInstance(parsed, list)

    def test_html_empty_file(self):
        """Test parsing empty HTML file"""
        with open(self.test_html_path, 'w') as f:
            f.write("")

        with patch('scripts.parser.webdriver.Chrome') as mock_driver:
            mock_driver_instance = MagicMock()
            mock_driver.return_value = mock_driver_instance
            mock_driver_instance.page_source = ""

            parsed = parse_html(self.test_html_path)
            self.assertEqual(len(parsed), 0)

    def test_html_missing_elements(self):
        """Test HTML with missing quiz elements"""
        content = """
        <html>
        <body>
        <div class="not-quiz-question">
            <p>This is not a quiz question</p>
        </div>
        </body>
        </html>
        """
        with open(self.test_html_path, 'w', encoding='utf-8') as f:
            f.write(content)

        with patch('scripts.parser.webdriver.Chrome') as mock_driver:
            mock_driver_instance = MagicMock()
            mock_driver.return_value = mock_driver_instance
            mock_driver_instance.page_source = content

            parsed = parse_html(self.test_html_path)
            self.assertEqual(len(parsed), 0)

    @patch('scripts.parser.webdriver.Chrome')
    def test_html_selenium_exception(self, mock_driver):
        """Test handling of Selenium WebDriver exceptions"""
        mock_driver.side_effect = WebDriverException("WebDriver error")

        with self.assertRaises(WebDriverException):
            parse_html(self.test_html_path)

    def test_html_url_parsing(self):
        """Test parsing HTML from URL"""
        test_url = "https://example.com/quiz.html"
        content = """
        <div class="quiz-question">
            <p class="question-text">Online quiz question</p>
            <p class="answer-text">Online answer</p>
        </div>
        """

        with patch('scripts.parser.webdriver.Chrome') as mock_driver:
            mock_driver_instance = MagicMock()
            mock_driver.return_value = mock_driver_instance
            mock_driver_instance.page_source = content

            parsed = parse_html(test_url)
            self.assertIsInstance(parsed, list)

    def test_html_dynamic_content(self):
        """Test HTML with dynamic JavaScript content"""
        content = """
        <html>
        <body>
        <div class="quiz-question" id="dynamic-quiz">
            <p class="question-text">Dynamic question loaded by JS</p>
            <p class="answer-text">Dynamic answer</p>
        </div>
        <script>
        // Simulate dynamic content loading
        document.getElementById('dynamic-quiz').innerHTML = 'Updated content';
        </script>
        </body>
        </html>
        """

        with patch('scripts.parser.webdriver.Chrome') as mock_driver:
            mock_driver_instance = MagicMock()
            mock_driver.return_value = mock_driver_instance
            mock_driver_instance.page_source = content

            parsed = parse_html(self.test_html_path)
            self.assertIsInstance(parsed, list)


class TestDataStructuringEdgeCases(unittest.TestCase):
    """Test edge cases for data structuring functionality"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_structuring_basic(self):
        """Test basic data structuring"""
        raw = [{"raw_question": "Q with \\(eq\\)", "raw_answer": "A"}]
        structured = structure_data(raw)
        self.assertIn('equations', structured[0])
        self.assertIn('type', structured[0])
        self.assertIn('question', structured[0])

    def test_structuring_empty_data(self):
        """Test structuring with empty data"""
        structured = structure_data([])
        self.assertEqual(len(structured), 0)

    def test_structuring_missing_fields(self):
        """Test structuring with missing required fields"""
        raw = [{"raw_question": "Question only"}]  # Missing raw_answer
        with self.assertRaises(KeyError):
            structure_data(raw)

    def test_structuring_multiple_choice(self):
        """Test structuring multiple choice questions"""
        raw = [{"raw_question": "What is (A) correct (B) wrong?", "raw_answer": "A"}]
        structured = structure_data(raw)
        self.assertEqual(structured[0]['type'], 'multiple-choice')

    def test_structuring_open_ended(self):
        """Test structuring open-ended questions"""
        raw = [{"raw_question": "Explain the concept", "raw_answer": "Detailed explanation"}]
        structured = structure_data(raw)
        self.assertEqual(structured[0]['type'], 'open-ended')

    def test_structuring_equations(self):
        """Test equation extraction and formatting"""
        raw = [{"raw_question": "Solve $x^2 + 1 = 0$ and $$\\int_0^1 x dx$$", "raw_answer": "Complex solutions"}]
        structured = structure_data(raw)
        self.assertGreater(len(structured[0]['equations']), 0)

    def test_structuring_tables(self):
        """Test table detection"""
        raw = [{"raw_question": "Analyze the table data", "raw_answer": "Table analysis"}]
        structured = structure_data(raw)
        self.assertGreater(len(structured[0]['tables']), 0)

    def test_structuring_figures(self):
        """Test figure detection"""
        raw = [{"raw_question": "Look at the figure", "raw_answer": "Figure description"}]
        structured = structure_data(raw)
        self.assertGreater(len(structured[0]['figures']), 0)

    def test_structuring_tikz_figures(self):
        """Test TikZ figure handling"""
        tikz_code = "\\begin{tikzpicture}\\node (A) at (0,0) {A};\\end{tikzpicture}"
        raw = [{"raw_question": "TikZ question", "raw_answer": "Answer", "figures": [tikz_code]}]
        structured = structure_data(raw)
        self.assertEqual(structured[0]['figures'], [tikz_code])

    @patch('builtins.open', side_effect=PermissionError("Permission denied"))
    def test_structuring_file_write_error(self, mock_open):
        """Test handling of file write errors"""
        raw = [{"raw_question": "Question", "raw_answer": "Answer"}]
        with self.assertRaises(PermissionError):
            structure_data(raw)

    def test_structuring_unicode_content(self):
        """Test structuring with Unicode content"""
        raw = [{"raw_question": "Quelle est la réponse? α + β = ?", "raw_answer": "Réponse française"}]
        structured = structure_data(raw)
        self.assertIn('question', structured[0])
        self.assertIn('answer', structured[0])


class TestFetcherEdgeCases(unittest.TestCase):
    """Test edge cases for document fetching functionality"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @patch('scripts.fetcher.requests.get')
    def test_fetch_pdf_success(self, mock_get):
        """Test successful PDF fetching"""
        mock_response = MagicMock()
        mock_response.content = b"PDF content"
        mock_get.return_value = mock_response

        with patch('os.makedirs'):
            result = fetch_document("https://example.com/test.pdf", "pdf")
            self.assertTrue(result.endswith('.pdf'))

    @patch('scripts.fetcher.requests.get')
    def test_fetch_html_success(self, mock_get):
        """Test successful HTML fetching"""
        mock_response = MagicMock()
        mock_response.text = "<html><body>Test</body></html>"
        mock_get.return_value = mock_response

        with patch('os.makedirs'):
            result = fetch_document("https://example.com/test.html", "html")
            self.assertTrue(result.endswith('.html'))

    @patch('scripts.fetcher.requests.get')
    def test_fetch_tex_success(self, mock_get):
        """Test successful TeX fetching"""
        mock_response = MagicMock()
        mock_response.text = "\\documentclass{article}\\begin{document}Test\\end{document}"
        mock_get.return_value = mock_response

        with patch('os.makedirs'):
            result = fetch_document("https://example.com/test.tex", "tex")
            self.assertTrue(result.endswith('.tex'))

    @patch('scripts.fetcher.requests.get')
    def test_fetch_network_error(self, mock_get):
        """Test network error handling"""
        mock_get.side_effect = requests.exceptions.ConnectionError("Network error")

        with self.assertRaises(requests.exceptions.ConnectionError):
            fetch_document("https://example.com/test.pdf", "pdf")

    @patch('scripts.fetcher.requests.get')
    def test_fetch_timeout_error(self, mock_get):
        """Test timeout error handling"""
        mock_get.side_effect = requests.exceptions.Timeout("Timeout error")

        with self.assertRaises(requests.exceptions.Timeout):
            fetch_document("https://example.com/test.pdf", "pdf")

    def test_fetch_unsupported_format(self):
        """Test unsupported format error"""
        with self.assertRaises(ValueError):
            fetch_document("https://example.com/test.doc", "doc")

    @patch('scripts.fetcher.requests.get')
    @patch('scripts.fetcher.BeautifulSoup')
    def test_simple_crawler_success(self, mock_soup, mock_get):
        """Test successful simple crawling"""
        mock_response = MagicMock()
        mock_response.text = "<html><body><a href='/exam1'>Exam 1</a></body></html>"
        mock_get.return_value = mock_response

        mock_soup_instance = MagicMock()
        mock_link = MagicMock()
        mock_link.text.lower.return_value = "exam 1"
        mock_link.__getitem__.return_value = "/exam1"
        mock_soup_instance.find_all.return_value = [mock_link]
        mock_soup.return_value = mock_soup_instance

        with patch('scripts.fetcher.fetch_document') as mock_fetch:
            mock_fetch.return_value = "downloaded_file.pdf"
            result = simple_crawler("https://example.com", max_docs=1)
            self.assertIsInstance(result, list)

    @patch('scripts.fetcher.requests.get')
    def test_simple_crawler_network_error(self, mock_get):
        """Test crawler network error handling"""
        mock_get.side_effect = requests.exceptions.ConnectionError("Network error")

        with self.assertRaises(requests.exceptions.ConnectionError):
            simple_crawler("https://example.com")


class TestVisualizerEdgeCases(unittest.TestCase):
    """Test edge cases for visualization functionality"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    @patch('scripts.visualizer.plt.savefig')
    @patch('scripts.visualizer.os.makedirs')
    def test_visualize_basic(self, mock_makedirs, mock_savefig):
        """Test basic visualization functionality"""
        structured_data = [
            {"type": "multiple-choice", "equations": ["$x^2$"], "tables": [], "figures": []},
            {"type": "open-ended", "equations": [], "tables": ["table"], "figures": []}
        ]

        # Should not raise any exceptions
        visualize_results(structured_data)

        # Check that plots were saved
        self.assertEqual(mock_savefig.call_count, 4)  # 4 different plots

    @patch('scripts.visualizer.plt.savefig')
    @patch('scripts.visualizer.os.makedirs')
    def test_visualize_empty_data(self, mock_makedirs, mock_savefig):
        """Test visualization with empty data"""
        visualize_results([])

        # Should still create plots even with empty data
        self.assertEqual(mock_savefig.call_count, 4)

    @patch('scripts.visualizer.plt.savefig', side_effect=PermissionError("Permission denied"))
    @patch('scripts.visualizer.os.makedirs')
    def test_visualize_file_write_error(self, mock_makedirs, mock_savefig):
        """Test visualization file write error handling"""
        structured_data = [{"type": "multiple-choice", "equations": [], "tables": [], "figures": []}]

        with self.assertRaises(PermissionError):
            visualize_results(structured_data)


class TestIntegrationEdgeCases(unittest.TestCase):
    """Test edge cases for end-to-end integration"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_parse_document_pdf(self):
        """Test parse_document function with PDF"""
        test_path = os.path.join(self.temp_dir, 'test.pdf')
        with open(test_path, 'w') as f:
            f.write("Question 1: Test?\nAnswer: Yes")

        result = parse_document(test_path, 'pdf')
        self.assertIsInstance(result, list)

    def test_parse_document_unsupported_format(self):
        """Test parse_document with unsupported format"""
        test_path = os.path.join(self.temp_dir, 'test.doc')
        with open(test_path, 'w') as f:
            f.write("Test content")

        with self.assertRaises(ValueError):
            parse_document(test_path, 'doc')

    def test_filename_mismatch_issue(self):
        """Test the filename mismatch issue in main.py"""
        expected_file = 'data/math_test.tex'
        actual_file = 'data/math_exam.tex'
        # This should fail if the file doesn't exist
        self.assertFalse(os.path.exists(expected_file))

    @patch('scripts.parser.parse_pdf')
    @patch('scripts.parser.parse_html')
    @patch('scripts.parser.parse_tex')
    def test_pipeline_error_propagation(self, mock_tex, mock_html, mock_pdf):
        """Test error propagation through the pipeline"""
        mock_pdf.side_effect = Exception("PDF parsing error")
        mock_html.side_effect = Exception("HTML parsing error")
        mock_tex.side_effect = Exception("TeX parsing error")

        with self.assertRaises(Exception):
            parse_document("test.pdf", "pdf")

        with self.assertRaises(Exception):
            parse_document("test.html", "html")

        with self.assertRaises(Exception):
            parse_document("test.tex", "tex")


class TestPerformanceAndStress(unittest.TestCase):
    """Test performance and stress scenarios"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_large_pdf_parsing(self):
        """Test parsing large PDF files"""
        large_content = "Question {}: What is {}?\nAnswer: {}\n" * 1000
        large_content = large_content.format(*range(3000))  # Generate 1000 questions

        test_path = os.path.join(self.temp_dir, 'large.pdf')
        with open(test_path, 'w') as f:
            f.write(large_content)

        import time
        start_time = time.time()
        result = parse_pdf(test_path)
        end_time = time.time()

        # Should complete within reasonable time (adjust threshold as needed)
        self.assertLess(end_time - start_time, 30)  # 30 seconds max
        self.assertIsInstance(result, list)

    def test_large_tex_parsing(self):
        """Test parsing large TeX files"""
        large_content = "\\question{{Question {}}}\n\\answer{{Answer {}}}\n" * 500
        large_content = large_content.format(*range(1000))

        test_path = os.path.join(self.temp_dir, 'large.tex')
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(large_content)

        import time
        start_time = time.time()
        result = parse_tex(test_path)
        end_time = time.time()

        # Should complete within reasonable time
        self.assertLess(end_time - start_time, 30)
        self.assertIsInstance(result, list)

    def test_memory_usage_large_dataset(self):
        """Test memory usage with large datasets"""
        import psutil
        import os

        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Create large dataset
        large_raw_data = []
        for i in range(1000):
            large_raw_data.append({
                "raw_question": f"Question {i}: " + "x" * 1000,  # Long questions
                "raw_answer": f"Answer {i}: " + "y" * 1000       # Long answers
            })

        # Process the data
        structured = structure_data(large_raw_data)

        # Check final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # Memory increase should be reasonable (adjust threshold as needed)
        self.assertLess(memory_increase, 500)  # Less than 500MB increase
        self.assertEqual(len(structured), 1000)

    def test_concurrent_parsing(self):
        """Test concurrent parsing operations"""
        import threading
        import time

        results = []
        errors = []

        def parse_worker(worker_id):
            try:
                content = f"Question {worker_id}: Test question?\nAnswer: Test answer"
                test_path = os.path.join(self.temp_dir, f'test_{worker_id}.pdf')
                with open(test_path, 'w') as f:
                    f.write(content)
                result = parse_pdf(test_path)
                results.append((worker_id, result))
            except Exception as e:
                errors.append((worker_id, str(e)))

        # Create multiple threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=parse_worker, args=(i,))
            threads.append(thread)

        # Start all threads
        start_time = time.time()
        for thread in threads:
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        end_time = time.time()

        # Check results
        self.assertEqual(len(errors), 0, f"Errors occurred: {errors}")
        self.assertEqual(len(results), 10)
        self.assertLess(end_time - start_time, 60)  # Should complete within 1 minute

    def test_edge_case_combinations(self):
        """Test combinations of edge cases"""
        # Combine multiple edge cases in one test
        edge_cases = [
            {"raw_question": "", "raw_answer": ""},  # Empty
            {"raw_question": "Q with $\\alpha + \\beta$", "raw_answer": "Math"},  # Math
            {"raw_question": "Français: é, à, ç", "raw_answer": "Accents"},  # Unicode
            {"raw_question": "Table question", "raw_answer": "Table answer"},  # Table
            {"raw_question": "Figure question", "raw_answer": "Figure answer"},  # Figure
            {"raw_question": "Multiple (A) choice (B) question", "raw_answer": "A"},  # MC
        ]

        structured = structure_data(edge_cases)

        # Should handle all edge cases without crashing
        self.assertEqual(len(structured), len(edge_cases))

        # Check that different types are detected
        types = [item['type'] for item in structured]
        self.assertIn('multiple-choice', types)
        self.assertIn('open-ended', types)


class TestRealWorldScenarios(unittest.TestCase):
    """Test real-world scenarios and data"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_french_math_content(self):
        """Test with French mathematical content similar to math_test.tex"""
        content = """
        \\question{Calculer la probabilité que la connexion soit stable et passe par le serveur B}
        \\answer{P(B ∩ S) = 0,15 × 0,80 = 0,12}

        \\question{Démontrer que $u_{n+1}=\\frac{2 u_{n}+1}{u_{n}+2}$}
        \\answer{Par récurrence sur n}
        """

        test_path = os.path.join(self.temp_dir, 'french_math.tex')
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(content)

        result = parse_tex(test_path)
        self.assertIsInstance(result, list)

        if result:
            structured = structure_data(result)
            self.assertGreater(len(structured), 0)

    def test_teas_quiz_format(self):
        """Test with TEAS quiz format similar to the HTML URL"""
        content = """
        <html>
        <body>
        <div class="quiz-question">
            <p class="question-text">Which sentence below is written in correct standard English?</p>
            <p class="answer-text">I am going to the store.</p>
        </div>
        <div class="quiz-question">
            <p class="question-text">Which organelle is responsible for the production of ATP in a cell?</p>
            <p class="answer-text">Mitochondria</p>
        </div>
        </body>
        </html>
        """

        with patch('scripts.parser.webdriver.Chrome') as mock_driver:
            mock_driver_instance = MagicMock()
            mock_driver.return_value = mock_driver_instance
            mock_driver_instance.page_source = content

            test_path = os.path.join(self.temp_dir, 'teas_quiz.html')
            with open(test_path, 'w', encoding='utf-8') as f:
                f.write(content)

            result = parse_html(test_path)
            self.assertIsInstance(result, list)

    def test_chemistry_pdf_content(self):
        """Test with chemistry content similar to chem_test.pdf"""
        content = """
        Question 1: What is the molecular formula for water?
        Answer: H2O

        Question 2: What is Avogadro's number?
        Answer: 6.022 × 10^23

        Question 3: Balance the equation: C + O2 → CO2
        Answer: C + O2 → CO2 (already balanced)
        """

        test_path = os.path.join(self.temp_dir, 'chemistry.pdf')
        with open(test_path, 'w') as f:
            f.write(content)

        result = parse_pdf(test_path)
        self.assertIsInstance(result, list)

        if result:
            structured = structure_data(result)
            self.assertGreater(len(structured), 0)


if __name__ == '__main__':
    # Run all tests
    unittest.main(verbosity=2)